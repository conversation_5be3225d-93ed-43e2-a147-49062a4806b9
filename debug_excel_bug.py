#!/usr/bin/env python3
"""
Debug the exact Excel generation bug by intercepting the data before and after Excel creation
"""

import sys
import os
sys.path.append('.')

from main import PDFToExcelConverter
import pandas as pd

def debug_excel_generation_bug():
    """Debug exactly what's happening in Excel generation"""
    
    print("🔍 DEBUGGING EXCEL GENERATION BUG")
    print("=" * 50)
    
    # Initialize converter
    converter = PDFToExcelConverter()
    converter.data_processor.set_debug_mode(True)
    
    # Process the PDF to get DataFrame
    pdf_files = ['pdfs/KOTHI SALE JULY-2025.pdf']
    
    # Extract text
    extracted_texts = converter.pdf_parser.extract_from_multiple_pdfs(pdf_files)
    
    if not extracted_texts:
        print("❌ Failed to extract text")
        return
    
    # Process data to get DataFrame
    df = converter.data_processor.process_multiple_files(extracted_texts)
    
    print(f"📊 Original DataFrame shape: {df.shape}")
    print(f"📋 DataFrame columns: {list(df.columns)}")
    
    # Check the _incomplete column
    if '_incomplete' in df.columns:
        incomplete_mask = df['_incomplete'] == 'true'
        incomplete_count = incomplete_mask.sum()
        main_count = len(df) - incomplete_count
        
        print(f"📊 Should be incomplete: {incomplete_count}")
        print(f"📊 Should be main: {main_count}")
        print(f"📊 Total: {len(df)}")
        print()
        
        # Test the exact Excel generator logic
        print("🔍 TESTING EXCEL GENERATOR LOGIC:")
        
        # Replicate the exact logic from excel_generator.py create_excel_file method
        if '_incomplete' in df.columns:
            # More robust filtering - handle both string and boolean values
            incomplete_mask = (df['_incomplete'] == 'true') | (df['_incomplete'] == True) | (df['_incomplete'].astype(str).str.lower() == 'true')
            incomplete_count = incomplete_mask.sum()
            
            # For main sheets, exclude incomplete records using robust filtering
            main_mask = ~incomplete_mask  # Invert the mask
            main_df = df[main_mask].copy()
            
            # Remove the _incomplete column from main data
            if '_incomplete' in main_df.columns:
                main_df = main_df.drop(columns=['_incomplete'])
            
            print(f"   📊 Incomplete mask result: {incomplete_count} records")
            print(f"   📊 Main mask result: {len(main_df)} records")
            print(f"   ✅ Separation calculation: {len(main_df) + incomplete_count} == {len(df)}")
            
            # Check if main_df has any records with 7 empty fields
            print("\n🔍 CHECKING MAIN_DF FOR 7 EMPTY FIELDS:")
            count_with_7_empty = 0
            key_fields = ['Property Code', 'Size/Yards', 'Contact Person', 'Phone Numbers', 'Property Type', 'Details', 'Status']
            
            for i, row in main_df.iterrows():
                empty_count = sum(1 for field in key_fields 
                                if pd.isna(row[field]) or row[field] == '' or str(row[field]) == 'nan')
                if empty_count == 7:
                    count_with_7_empty += 1
                    if count_with_7_empty <= 3:  # Show first 3
                        print(f"   ❌ Record {i}: 7 empty fields found in main_df!")
                        print(f"      Location: {row['Location/Area']}")
                        print(f"      Original _incomplete: {df.loc[i, '_incomplete'] if i in df.index else 'N/A'}")
            
            print(f"   Total main records with 7 empty fields: {count_with_7_empty}")
            
            # If we found any, there's a bug in the mask logic
            if count_with_7_empty > 0:
                print("\n❌ BUG FOUND: Records with 7 empty fields are in main_df")
                print("This means the incomplete_mask is not working correctly")
                
                # Let's check the _incomplete column values for these problematic records
                print("\n🔍 CHECKING _INCOMPLETE VALUES FOR PROBLEMATIC RECORDS:")
                for i, row in main_df.iterrows():
                    empty_count = sum(1 for field in key_fields 
                                    if pd.isna(row[field]) or row[field] == '' or str(row[field]) == 'nan')
                    if empty_count == 7:
                        original_incomplete = df.loc[i, '_incomplete'] if i in df.index else 'NOT_FOUND'
                        print(f"   Record {i}: _incomplete = '{original_incomplete}' (type: {type(original_incomplete)})")
                        print(f"   Mask test: {original_incomplete} == 'true' → {original_incomplete == 'true'}")
                        print(f"   Mask test: {original_incomplete} == True → {original_incomplete == True}")
                        print(f"   Mask test: str.lower() == 'true' → {str(original_incomplete).lower() == 'true'}")
            else:
                print("\n✅ No bug found in main_df separation")
        
    else:
        print("❌ '_incomplete' column NOT found")

if __name__ == "__main__":
    debug_excel_generation_bug() 