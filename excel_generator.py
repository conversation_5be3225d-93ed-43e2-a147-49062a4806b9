"""
Excel Generator Module
Creates beautifully formatted Excel files from structured data with comprehensive logging
"""

import os
import logging
from typing import Dict, Any, Optional
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.table import Table, TableStyleInfo
from openpyxl.chart import BarChart, Reference
from config import EXCEL_FORMATTING, EXCEL_COLUMNS, PROCESSING_CONFIG

# Configure logging
logger = logging.getLogger(__name__)

class ExcelGenerator:
    """
    Generates beautifully formatted Excel files from structured data with detailed logging
    """
    
    def __init__(self):
        self.formatting = EXCEL_FORMATTING
        self.output_filename = PROCESSING_CONFIG['output_filename']
        self.generation_stats = {}
        
        # Define styles
        self.header_font = Font(
            name=self.formatting['font_name'],
            size=self.formatting['header_font_size'],
            bold=True,
            color='FFFFFF'
        )
        
        self.data_font = Font(
            name=self.formatting['font_name'],
            size=self.formatting['font_size']
        )
        
        self.header_fill = PatternFill(
            start_color=self.formatting['header_color'],
            end_color=self.formatting['header_color'],
            fill_type='solid'
        )
        
        self.alternate_fill = PatternFill(
            start_color=self.formatting['alternate_row_color'],
            end_color=self.formatting['alternate_row_color'],
            fill_type='solid'
        )
        
        self.border = Border(
            left=Side(border_style='thin', color=self.formatting['border_color']),
            right=Side(border_style='thin', color=self.formatting['border_color']),
            top=Side(border_style='thin', color=self.formatting['border_color']),
            bottom=Side(border_style='thin', color=self.formatting['border_color'])
        )
        
        self.center_alignment = Alignment(horizontal='center', vertical='center')
        self.left_alignment = Alignment(horizontal='left', vertical='center')
    
    def create_summary_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Create summary statistics from the DataFrame with logging
        
        Args:
            df: Main data DataFrame
            
        Returns:
            dict: Summary statistics
        """
        try:
            logger.info(f"📊 CREATING SUMMARY DATA from {len(df)} records")
            
            summary = {
                'total_records': len(df),
                'total_files': df['File Source'].nunique() if 'File Source' in df.columns else 0,
                'property_categories': df['Property Category'].value_counts().to_dict() if 'Property Category' in df.columns else {},
                'locations': df['Location/Area'].value_counts().head(10).to_dict() if 'Location/Area' in df.columns else {},
                'months': df['Month/Year'].value_counts().to_dict() if 'Month/Year' in df.columns else {},
                'contact_stats': {
                    'with_contact': len(df[df['Contact Person'].str.len() > 0]) if 'Contact Person' in df.columns else 0,
                    'with_phone': len(df[df['Phone Numbers'].str.len() > 0]) if 'Phone Numbers' in df.columns else 0
                }
            }
            
            # Log summary details
            logger.info(f"   📈 Total records: {summary['total_records']}")
            logger.info(f"   📁 Source files: {summary['total_files']}")
            logger.info(f"   🏢 Locations: {len(summary['locations'])}")
            logger.info(f"   📅 Time periods: {len(summary['months'])}")
            logger.info(f"   👤 Records with contact: {summary['contact_stats']['with_contact']}")
            logger.info(f"   📞 Records with phone: {summary['contact_stats']['with_phone']}")
            
            # Log category breakdown
            if summary['property_categories']:
                logger.info(f"   🏠 Property categories:")
                for category, count in summary['property_categories'].items():
                    logger.info(f"      - {category}: {count} records")
            
            return summary
            
        except Exception as e:
            logger.error(f"❌ Error creating summary data: {str(e)}")
            return {}
    
    def create_summary_sheet(self, workbook: Workbook, summary_data: Dict[str, Any]) -> None:
        """
        Create a summary sheet with statistics and charts with logging
        
        Args:
            workbook: Excel workbook object
            summary_data: Summary statistics dictionary
        """
        try:
            logger.info(f"📋 CREATING SUMMARY SHEET")
            
            # Create summary sheet
            summary_sheet = workbook.create_sheet("Summary", 0)
            
            # Add title
            summary_sheet['A1'] = "Real Estate Data Summary"
            summary_sheet['A1'].font = Font(size=16, bold=True)
            summary_sheet.merge_cells('A1:B1')
            
            # Add summary statistics
            row = 3
            broken_count = summary_data.get('broken_records', 0)
            total_records = summary_data.get('total_records', 0)
            
            summary_items = [
                ('Total Records Processed', total_records + broken_count),
                ('Main Records', total_records),
                ('Broken Records', broken_count),
                ('Source Files', summary_data.get('total_files', 0)),
                ('Unique Locations', len(summary_data.get('locations', {}))),
                ('Time Periods', len(summary_data.get('months', {}))),
                ('Records with Contact Info', summary_data.get('contact_stats', {}).get('with_contact', 0)),
                ('Records with Phone Numbers', summary_data.get('contact_stats', {}).get('with_phone', 0))
            ]
            
            for label, value in summary_items:
                summary_sheet[f'A{row}'] = label
                summary_sheet[f'B{row}'] = value
                summary_sheet[f'A{row}'].font = self.data_font
                summary_sheet[f'B{row}'].font = self.data_font
                row += 1
            
            # Add property categories section
            row += 2
            summary_sheet[f'A{row}'] = "Property Categories"
            summary_sheet[f'A{row}'].font = Font(size=12, bold=True)
            row += 1
            
            categories = summary_data.get('property_categories', {})
            for category, count in categories.items():
                summary_sheet[f'A{row}'] = category
                summary_sheet[f'B{row}'] = count
                summary_sheet[f'A{row}'].font = self.data_font
                summary_sheet[f'B{row}'].font = self.data_font
                row += 1
            
            # Add top locations section
            row += 2
            summary_sheet[f'A{row}'] = "Top Locations"
            summary_sheet[f'A{row}'].font = Font(size=12, bold=True)
            row += 1
            
            locations = summary_data.get('locations', {})
            for location, count in list(locations.items())[:10]:  # Top 10
                summary_sheet[f'A{row}'] = location
                summary_sheet[f'B{row}'] = count
                summary_sheet[f'A{row}'].font = self.data_font
                summary_sheet[f'B{row}'].font = self.data_font
                row += 1
            
            # Auto-adjust column widths
            for column in summary_sheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                summary_sheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"   ✅ Summary sheet created with {row-3} data rows")
            
        except Exception as e:
            logger.error(f"❌ Error creating summary sheet: {str(e)}")
    
    def create_data_sheet(self, workbook: Workbook, df: pd.DataFrame, sheet_name: str = "All Data") -> None:
        """
        Create a data sheet with all records and formatting with logging
        
        Args:
            workbook: Excel workbook object
            df: DataFrame with data
            sheet_name: Name for the sheet
        """
        try:
            logger.info(f"📊 CREATING DATA SHEET: '{sheet_name}' with {len(df)} records")
            
            # Validate data integrity before creating sheet
            expected_columns = set(EXCEL_COLUMNS)
            actual_columns = set(df.columns)
            missing_columns = expected_columns - actual_columns
            extra_columns = actual_columns - expected_columns
            
            if missing_columns:
                logger.warning(f"   ⚠️  Missing columns: {missing_columns}")
            if extra_columns:
                logger.info(f"   ℹ️  Extra columns: {extra_columns}")
            
            # Log data integrity checks
            non_empty_counts = {}
            for col in EXCEL_COLUMNS:
                if col in df.columns:
                    try:
                        non_empty = len(df[df[col].notna() & (df[col] != "")])
                        non_empty_counts[col] = non_empty
                        completion_rate = (non_empty / len(df) * 100) if len(df) > 0 else 0
                        logger.debug(f"      {col}: {non_empty}/{len(df)} records ({completion_rate:.1f}%)")
                    except Exception:
                        non_empty_counts[col] = 0
            
            # Create sheet
            sheet = workbook.create_sheet(sheet_name)
            
            # Add headers
            for col_num, column_name in enumerate(EXCEL_COLUMNS, 1):
                cell = sheet.cell(row=1, column=col_num, value=column_name)
                cell.font = self.header_font
                cell.fill = self.header_fill
                cell.border = self.border
                cell.alignment = self.center_alignment
            
            # Add data rows
            rows_added = 0
            data_validation_errors = 0
            
            # Reset index to ensure contiguous row numbering after filtering
            df_reset = df.reset_index(drop=True)
            
            for df_row_num, (original_idx, row) in enumerate(df_reset.iterrows()):
                excel_row = df_row_num + 2  # +2 because Excel is 1-indexed and we have a header
                
                try:
                    for col_num, column_name in enumerate(EXCEL_COLUMNS, 1):
                        if column_name in df_reset.columns:
                            value = row[column_name]
                            # Convert to string and handle None/NaN
                            if pd.isna(value) or value is None:
                                value = ""
                            else:
                                value = str(value)
                        else:
                            value = ""
                        
                        cell = sheet.cell(row=excel_row, column=col_num, value=value)
                        cell.font = self.data_font
                        cell.border = self.border
                        cell.alignment = self.left_alignment
                        
                        # Apply alternating row colors
                        if excel_row % 2 == 0:
                            cell.fill = self.alternate_fill
                    
                    rows_added += 1
                    
                except Exception as e:
                    logger.warning(f"   ⚠️  Error adding row {df_row_num}: {str(e)}")
                    data_validation_errors += 1
                    continue
            
            # Auto-adjust column widths
            for column in sheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                sheet.column_dimensions[column_letter].width = adjusted_width
            
            # Log completion statistics
            logger.info(f"   ✅ Data sheet '{sheet_name}' created:")
            logger.info(f"      📊 Rows added: {rows_added}/{len(df)}")
            if data_validation_errors > 0:
                logger.warning(f"      ⚠️  Data errors: {data_validation_errors}")
            
            # Log field completion rates
            logger.info(f"      📈 Field completion rates:")
            for col, count in non_empty_counts.items():
                rate = (count / len(df) * 100) if len(df) > 0 else 0
                logger.info(f"         {col}: {rate:.1f}% ({count}/{len(df)})")
            
        except Exception as e:
            logger.error(f"❌ Error creating data sheet '{sheet_name}': {str(e)}")
    
    def create_category_sheets(self, workbook: Workbook, df: pd.DataFrame) -> None:
        """
        Create separate sheets for each property category with logging
        
        Args:
            workbook: Excel workbook object
            df: DataFrame with data
        """
        try:
            logger.info(f"🏠 CREATING CATEGORY SHEETS")
            
            if 'Property Category' not in df.columns:
                logger.warning(f"   ⚠️  No 'Property Category' column found, skipping category sheets")
                return
            
            categories = df['Property Category'].value_counts()
            logger.info(f"   📋 Found {len(categories)} categories:")
            
            sheets_created = 0
            for category in categories.index:
                try:
                    if pd.isna(category) or category == "":
                        category_name = "Unknown"
                    else:
                        category_name = str(category)
                    
                    category_df = df[df['Property Category'] == category]
                    record_count = len(category_df)
                    
                    logger.info(f"      📊 Creating '{category_name}': {record_count} records")
                    
                    # Clean sheet name (Excel has restrictions)
                    safe_sheet_name = category_name.replace('/', '_').replace('\\', '_')[:31]
                    
                    self.create_data_sheet(workbook, category_df, safe_sheet_name)
                    sheets_created += 1
                    
                except Exception as e:
                    logger.error(f"   ❌ Error creating sheet for category '{category}': {str(e)}")
                    continue
            
            logger.info(f"   ✅ Created {sheets_created}/{len(categories)} category sheets")
            
        except Exception as e:
            logger.error(f"❌ Error creating category sheets: {str(e)}")
    
    def create_incomplete_records_sheet(self, workbook: Workbook, df: pd.DataFrame) -> None:
        """
        Create a sheet for incomplete/minimal records that didn't meet main criteria
        
        Args:
            workbook: Excel workbook object
            df: DataFrame with data (will filter for incomplete records)
        """
        try:
            logger.info(f"🔧 CREATING INCOMPLETE RECORDS SHEET")
            logger.info(f"   📊 Input DataFrame shape: {df.shape}")
            logger.info(f"   📋 Available columns: {list(df.columns)}")
            
            # Check if _incomplete column exists
            if '_incomplete' in df.columns:
                logger.info(f"   ✅ Found '_incomplete' column")
                
                # Count incomplete records
                incomplete_mask = df['_incomplete'] == 'true'
                incomplete_count = incomplete_mask.sum()
                logger.info(f"   📊 Total incomplete records found: {incomplete_count}")
                
                if incomplete_count > 0:
                    incomplete_df = df[incomplete_mask].copy()
                    # Remove the internal flag column
                    if '_incomplete' in incomplete_df.columns:
                        incomplete_df = incomplete_df.drop(columns=['_incomplete'])
                    
                    logger.info(f"   📊 Creating sheet with {len(incomplete_df)} incomplete records")
                    
                    # Create the data sheet for incomplete records
                    self.create_data_sheet(workbook, incomplete_df, "Incomplete Records")
                    
                    logger.info(f"   ✅ Incomplete records sheet created successfully")
                    return
                else:
                    logger.info(f"   ℹ️  No incomplete records to process (count: {incomplete_count})")
            else:
                logger.warning(f"   ⚠️  '_incomplete' column not found in DataFrame")
                
                # Debug: Show a sample of the DataFrame
                logger.info(f"   🔍 Sample data (first 3 rows):")
                for i, row in df.head(3).iterrows():
                    logger.info(f"      Row {i}: {dict(row)}")
            
            logger.info(f"   ℹ️  No incomplete records sheet created")
            
        except Exception as e:
            logger.error(f"❌ Error creating incomplete records sheet: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
    
    def create_broken_records_sheet(self, workbook: Workbook, broken_df: pd.DataFrame) -> None:
        """
        Create a sheet for broken records that have too many missing key fields
        
        Args:
            workbook: Excel workbook object
            broken_df: DataFrame with broken records data
        """
        try:
            logger.info(f"🚨 CREATING BROKEN RECORDS SHEET")
            logger.info(f"   📊 Input DataFrame shape: {broken_df.shape}")
            logger.info(f"   📋 Available columns: {list(broken_df.columns)}")
            
            if len(broken_df) > 0:
                logger.info(f"   📊 Creating sheet with {len(broken_df)} broken records")
                
                # Create the data sheet for broken records
                self.create_data_sheet(workbook, broken_df, "Broken Records")
                
                logger.info(f"   ✅ Broken records sheet created successfully")
                logger.info(f"   💡 These records have >3 missing key fields but are saved for review")
            else:
                logger.info(f"   ℹ️  No broken records to process")
            
        except Exception as e:
            logger.error(f"❌ Error creating broken records sheet: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    def create_excel_file(self, df: pd.DataFrame, output_path: Optional[str] = None, 
                          include_summary: bool = True, create_category_sheets: bool = True,
                          include_incomplete_records: bool = True) -> str:
        """
        Create a beautifully formatted Excel file from DataFrame with comprehensive logging and incomplete records
        
        Args:
            df: DataFrame with structured data
            output_path: Optional custom output path
            include_summary: Include summary sheet
            create_category_sheets: Create separate category sheets
            include_incomplete_records: Include incomplete records sheet
            
        Returns:
            str: Path to created Excel file
        """
        try:
            if output_path is None:
                output_path = self.output_filename
            
            # Ensure output directory exists
            output_dir = os.path.dirname(output_path) if output_path and os.path.dirname(output_path) else '.'
            os.makedirs(output_dir, exist_ok=True)
            
            logger.info(f"💾 CREATING EXCEL FILE: {output_path}")
            logger.info(f"   📊 Input DataFrame: {len(df)} records")
            
            # Separate main records from broken records
            if '_broken' in df.columns:
                broken_df = df[df['_broken'] == 'true'].copy()
                main_df = df[df['_broken'] != 'true'].copy()
            else:
                broken_df = pd.DataFrame()
                main_df = df.copy()
            
            # Remove the _broken column from both DataFrames
            if '_broken' in main_df.columns:
                main_df = main_df.drop(columns=['_broken'])
            if '_broken' in broken_df.columns:
                broken_df = broken_df.drop(columns=['_broken'])
            
            logger.info(f"   📊 Total records: {len(df)}")
            logger.info(f"   📊 Main records: {len(main_df)}")
            logger.info(f"   📊 Broken records: {len(broken_df)}")
            
            if len(broken_df) > 0:
                logger.info(f"   🚨 Broken records will be saved to separate sheet")
            
            # Create workbook
            workbook = Workbook()
            
            # Remove default sheet
            if "Sheet" in workbook.sheetnames:
                workbook.remove(workbook["Sheet"])
            
            # Create summary data and sheet if enabled
            if include_summary:
                summary_data = self.create_summary_data(main_df)
                summary_data['broken_records'] = len(broken_df)  # Add broken records count
                self.create_summary_sheet(workbook, summary_data)
            
            # Create main data sheet with all records
            logger.info(f"🔍 DEBUG: About to create main sheet with main_df shape: {main_df.shape}")
            logger.info(f"🔍 DEBUG: main_df columns: {list(main_df.columns)}")
            logger.info(f"🔍 DEBUG: All records will be included in main sheet")
                
            self.create_data_sheet(workbook, main_df, "All Property Data")
            
            # Create broken records sheet if there are any broken records
            if len(broken_df) > 0:
                self.create_broken_records_sheet(workbook, broken_df)
            
            # Create filtered views by category if enabled (using main records only)
            if create_category_sheets:
                self.create_category_sheets(workbook, main_df)
            
            # Save the workbook
            final_output_path = output_path if output_path else self.output_filename
            workbook.save(final_output_path)
            workbook.close()
            
            # Log final statistics
            file_size_mb = os.path.getsize(final_output_path) / (1024 * 1024)
            total_sheets = len(workbook.sheetnames)
                
            logger.info(f"✅ EXCEL FILE CREATED SUCCESSFULLY!")
            logger.info(f"   📁 File: {final_output_path}")
            logger.info(f"   💾 Size: {file_size_mb:.2f} MB")
            logger.info(f"   📊 Total sheets: {total_sheets}")
            logger.info(f"   📋 Main records: {len(main_df)}")
            if len(broken_df) > 0:
                logger.info(f"   🚨 Broken records: {len(broken_df)} (in separate sheet)")
            
            return final_output_path
            
        except Exception as e:
            error_msg = f"❌ Error creating Excel file: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def add_validation_sheet(self, workbook: Workbook, processing_errors: list) -> None:
        """
        Add a validation sheet with processing errors and warnings
        
        Args:
            workbook: Excel workbook object
            processing_errors: List of processing errors
        """
        try:
            if not processing_errors:
                return
            
            ws = workbook.create_sheet(title="Validation Errors")
            
            # Headers
            ws['A1'] = "Processing Errors and Warnings"
            ws['A1'].font = Font(name=self.formatting['font_name'], size=14, bold=True, color='C73E1D')
            
            ws['A3'] = "Error/Warning"
            ws['A3'].font = self.header_font
            ws['A3'].fill = PatternFill(start_color='FFC73E1D', end_color='FFC73E1D', fill_type='solid')
            
            # Add errors
            for i, error in enumerate(processing_errors, 4):
                ws[f'A{i}'] = error
                ws[f'A{i}'].font = self.data_font
            
            # Auto-adjust column width
            ws.column_dimensions['A'].width = 80
            
            logger.info(f"   ✅ Added validation sheet with {len(processing_errors)} errors")
            
        except Exception as e:
            logger.error(f"❌ Error creating validation sheet: {str(e)}")

# Example usage and testing
if __name__ == "__main__":
    # Create sample data
    sample_data = {
        'File Source': ['KOTHI SALE JUNE-2025.pdf', 'RESI RENT JULY-2025.pdf'],
        'Property Category': ['Kothi Sale', 'Residential Rent'],
        'Month/Year': ['June 2025', 'July 2025'],
        'Location/Area': ['MAHARANI BAGH', 'NEW FRIENDS COLONY'],
        'Property Code': ['B-BLK', 'A-287'],
        'Size/Yards': ['1150Y', '500Y'],
        'Property Type': ['Duplex', 'House'],
        'Details': ['BMT+GF+FF+SF+TF, 3BHK', 'GF+FF+SF'],
        'Contact Person': ['R K SHARMA', 'GREWAL'],
        'Phone Numbers': ['9810411268', '9569669999'],
        'Status': ['P/D', 'NOT CONFIRM']
    }
    
    df = pd.DataFrame(sample_data)
    
    generator = ExcelGenerator()
    output_file = generator.create_excel_file(df, "test_output.xlsx")
    print(f"Created test Excel file: {output_file}") 