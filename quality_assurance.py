"""
Quality Assurance System for PDF to Excel Validation
Provides comprehensive reporting, monitoring, and quality metrics
"""

import json
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from datetime import datetime, timedelta
import sqlite3
from dataclasses import dataclass, asdict
import pandas as pd

from pattern_validator import ValidationReport, ValidationIssue
from smart_validator import SmartValidationPipeline

logger = logging.getLogger(__name__)

@dataclass
class QualityMetrics:
    """Quality metrics for validation results"""
    accuracy_score: float
    completeness_score: float
    consistency_score: float
    reliability_score: float
    overall_quality_score: float
    issues_by_severity: Dict[str, int]
    processing_efficiency: float
    recommendation_score: float

@dataclass
class ValidationSession:
    """Single validation session record"""
    session_id: str
    timestamp: datetime
    pdf_file: str
    excel_file: str
    pdf_records: int
    excel_records: int
    match_rate: float
    issues_found: int
    corrections_applied: int
    processing_time: float
    quality_metrics: QualityMetrics
    ai_used: bool

class QualityAssuranceSystem:
    """
    Comprehensive quality assurance system for validation pipeline
    """
    
    def __init__(self, db_path: str = "validation_qa.db"):
        self.db_path = db_path
        self.setup_database()
        
        # Quality thresholds
        self.quality_thresholds = {
            "excellent": 95.0,    # 95%+ accuracy
            "good": 85.0,         # 85-95% accuracy
            "acceptable": 70.0,   # 70-85% accuracy
            "poor": 50.0,         # 50-70% accuracy
            # Below 50% is considered "unacceptable"
        }
        
        # Issue severity weights for scoring
        self.severity_weights = {
            "critical": 10.0,
            "high": 5.0,
            "medium": 2.0,
            "low": 1.0
        }
        
        logger.info(f"🎯 Quality Assurance System initialized (DB: {db_path})")
    
    def setup_database(self):
        """Setup SQLite database for quality tracking"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create validation sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS validation_sessions (
                    session_id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    pdf_file TEXT NOT NULL,
                    excel_file TEXT NOT NULL,
                    pdf_records INTEGER,
                    excel_records INTEGER,
                    match_rate REAL,
                    issues_found INTEGER,
                    corrections_applied INTEGER,
                    processing_time REAL,
                    quality_score REAL,
                    ai_used BOOLEAN,
                    quality_data TEXT
                )
            """)
            
            # Create issues table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS validation_issues (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT,
                    issue_type TEXT,
                    severity TEXT,
                    description TEXT,
                    confidence REAL,
                    resolved BOOLEAN DEFAULT FALSE,
                    FOREIGN KEY (session_id) REFERENCES validation_sessions (session_id)
                )
            """)
            
            # Create quality trends table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS quality_trends (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT,
                    avg_accuracy REAL,
                    total_validations INTEGER,
                    total_issues INTEGER,
                    total_corrections INTEGER,
                    ai_usage_rate REAL
                )
            """)
            
            conn.commit()
            conn.close()
            
            logger.info("✅ QA database initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup QA database: {str(e)}")
    
    def assess_validation_quality(self, validation_results: Dict[str, Any]) -> QualityMetrics:
        """
        Comprehensive quality assessment of validation results
        
        Args:
            validation_results: Results from smart validation pipeline
            
        Returns:
            Quality metrics assessment
        """
        report = validation_results.get('validation_report')
        if not report:
            return QualityMetrics(0, 0, 0, 0, 0, {}, 0, 0)
        
        # 1. Accuracy Score (based on match rate)
        accuracy_score = min(report.match_rate, 100.0)
        
        # 2. Completeness Score (how many records were captured)
        if report.total_pdf_records > 0:
            completeness_score = min(
                (report.total_excel_records / report.total_pdf_records) * 100, 100.0
            )
        else:
            completeness_score = 0.0
        
        # 3. Consistency Score (based on issue severity)
        issues_by_severity = {}
        total_severity_weight = 0
        max_possible_weight = len(report.issues) * self.severity_weights["critical"]
        
        for issue in report.issues:
            severity = issue.severity
            issues_by_severity[severity] = issues_by_severity.get(severity, 0) + 1
            total_severity_weight += self.severity_weights.get(severity, 1.0)
        
        if max_possible_weight > 0:
            consistency_score = max(0, 100 - (total_severity_weight / max_possible_weight * 100))
        else:
            consistency_score = 100.0
        
        # 4. Reliability Score (based on confidence of fixes)
        high_confidence_fixes = len([
            i for i in report.issues 
            if i.suggested_fix and i.confidence >= 0.8
        ])
        total_fixable_issues = len([i for i in report.issues if i.suggested_fix])
        
        if total_fixable_issues > 0:
            reliability_score = (high_confidence_fixes / total_fixable_issues) * 100
        else:
            reliability_score = 100.0 if len(report.issues) == 0 else 50.0
        
        # 5. Processing Efficiency (records per second)
        processing_time = validation_results.get('processing_time', 1.0)
        records_processed = report.total_pdf_records + report.total_excel_records
        processing_efficiency = records_processed / processing_time if processing_time > 0 else 0
        
        # 6. Recommendation Score (actionability of recommendations)
        recommendations = validation_results.get('recommendations', [])
        actionable_recs = len([r for r in recommendations if r.get('time_required') != 'Unknown'])
        total_recs = len(recommendations)
        
        recommendation_score = (actionable_recs / total_recs * 100) if total_recs > 0 else 100.0
        
        # 7. Overall Quality Score (weighted average)
        weights = {
            'accuracy': 0.35,      # Most important
            'completeness': 0.25,  # Second most important
            'consistency': 0.20,   # Third
            'reliability': 0.15,   # Fourth
            'efficiency': 0.05     # Least important for quality
        }
        
        overall_quality_score = (
            accuracy_score * weights['accuracy'] +
            completeness_score * weights['completeness'] +
            consistency_score * weights['consistency'] +
            reliability_score * weights['reliability'] +
            min(processing_efficiency * 10, 100) * weights['efficiency']  # Cap efficiency impact
        )
        
        return QualityMetrics(
            accuracy_score=accuracy_score,
            completeness_score=completeness_score,
            consistency_score=consistency_score,
            reliability_score=reliability_score,
            overall_quality_score=overall_quality_score,
            issues_by_severity=issues_by_severity,
            processing_efficiency=processing_efficiency,
            recommendation_score=recommendation_score
        )
    
    def record_validation_session(self, validation_results: Dict[str, Any], 
                                pdf_path: str, excel_path: str) -> ValidationSession:
        """
        Record a validation session in the quality database
        
        Args:
            validation_results: Results from validation
            pdf_path: Source PDF path
            excel_path: Excel file path
            
        Returns:
            ValidationSession record
        """
        report = validation_results.get('validation_report')
        if not report:
            logger.error("❌ Cannot record session without validation report")
            return None
        
        # Generate session data
        session_id = f"session_{int(time.time())}_{hash(pdf_path) & 0x7FFFFFFF}"
        timestamp = datetime.now()
        quality_metrics = self.assess_validation_quality(validation_results)
        
        session = ValidationSession(
            session_id=session_id,
            timestamp=timestamp,
            pdf_file=Path(pdf_path).name,
            excel_file=Path(excel_path).name,
            pdf_records=report.total_pdf_records,
            excel_records=report.total_excel_records,
            match_rate=report.match_rate,
            issues_found=len(report.issues),
            corrections_applied=validation_results.get('corrections_applied', 0),
            processing_time=validation_results.get('processing_time', 0.0),
            quality_metrics=quality_metrics,
            ai_used=self._was_ai_used(validation_results)
        )
        
        # Store in database
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Insert session record
            cursor.execute("""
                INSERT INTO validation_sessions 
                (session_id, timestamp, pdf_file, excel_file, pdf_records, excel_records,
                 match_rate, issues_found, corrections_applied, processing_time, 
                 quality_score, ai_used, quality_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                session.session_id,
                session.timestamp.isoformat(),
                session.pdf_file,
                session.excel_file,
                session.pdf_records,
                session.excel_records,
                session.match_rate,
                session.issues_found,
                session.corrections_applied,
                session.processing_time,
                session.quality_metrics.overall_quality_score,
                session.ai_used,
                json.dumps(asdict(session.quality_metrics))
            ))
            
            # Insert individual issues
            for issue in report.issues:
                cursor.execute("""
                    INSERT INTO validation_issues 
                    (session_id, issue_type, severity, description, confidence)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    session.session_id,
                    issue.issue_type,
                    issue.severity,
                    issue.description,
                    issue.confidence
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"📊 Recorded validation session: {session_id} (Quality: {quality_metrics.overall_quality_score:.1f}%)")
            
        except Exception as e:
            logger.error(f"❌ Failed to record validation session: {str(e)}")
        
        return session
    
    def _was_ai_used(self, validation_results: Dict[str, Any]) -> bool:
        """Check if AI was used in validation"""
        # Look for AI indicators in the results
        stats = validation_results.get('statistics', {})
        ai_enhanced = stats.get('validation_methods', {}).get('ai_enhanced', 0)
        return ai_enhanced > 0
    
    def generate_quality_report(self, session_id: Optional[str] = None, 
                              days: int = 30) -> Dict[str, Any]:
        """
        Generate comprehensive quality report
        
        Args:
            session_id: Specific session to report on (optional)
            days: Number of days to include in trend analysis
            
        Returns:
            Comprehensive quality report
        """
        try:
            conn = sqlite3.connect(self.db_path)
            
            if session_id:
                # Single session report
                return self._generate_session_report(conn, session_id)
            else:
                # Multi-session trend report
                return self._generate_trend_report(conn, days)
                
        except Exception as e:
            logger.error(f"❌ Failed to generate quality report: {str(e)}")
            return {"error": str(e)}
        finally:
            conn.close()
    
    def _generate_session_report(self, conn: sqlite3.Connection, session_id: str) -> Dict[str, Any]:
        """Generate report for a specific session"""
        cursor = conn.cursor()
        
        # Get session data
        cursor.execute("""
            SELECT * FROM validation_sessions WHERE session_id = ?
        """, (session_id,))
        
        session_data = cursor.fetchone()
        if not session_data:
            return {"error": f"Session {session_id} not found"}
        
        # Get session issues
        cursor.execute("""
            SELECT issue_type, severity, description, confidence 
            FROM validation_issues WHERE session_id = ?
        """, (session_id,))
        
        issues = cursor.fetchall()
        
        # Parse quality data
        quality_data = json.loads(session_data[12]) if session_data[12] else {}
        
        return {
            "session_id": session_id,
            "file_info": {
                "pdf_file": session_data[2],
                "excel_file": session_data[3],
                "processing_time": session_data[9]
            },
            "accuracy_metrics": {
                "match_rate": session_data[6],
                "pdf_records": session_data[4],
                "excel_records": session_data[5],
                "issues_found": session_data[7],
                "corrections_applied": session_data[8]
            },
            "quality_assessment": {
                "overall_score": session_data[10],
                "quality_level": self._get_quality_level(session_data[10]),
                "detailed_metrics": quality_data
            },
            "issues_breakdown": [
                {"type": issue[0], "severity": issue[1], "description": issue[2], "confidence": issue[3]}
                for issue in issues
            ],
            "ai_usage": session_data[11],
            "timestamp": session_data[1]
        }
    
    def _generate_trend_report(self, conn: sqlite3.Connection, days: int) -> Dict[str, Any]:
        """Generate trend analysis report"""
        cursor = conn.cursor()
        
        # Get recent sessions
        cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
        
        cursor.execute("""
            SELECT COUNT(*), AVG(match_rate), AVG(quality_score), AVG(processing_time),
                   SUM(issues_found), SUM(corrections_applied), 
                   SUM(CASE WHEN ai_used THEN 1 ELSE 0 END)
            FROM validation_sessions 
            WHERE timestamp >= ?
        """, (cutoff_date,))
        
        trend_data = cursor.fetchone()
        
        if not trend_data or trend_data[0] == 0:
            return {"error": f"No validation sessions found in the last {days} days"}
        
        # Get quality distribution
        cursor.execute("""
            SELECT quality_score FROM validation_sessions 
            WHERE timestamp >= ?
        """, (cutoff_date,))
        
        quality_scores = [row[0] for row in cursor.fetchall()]
        
        # Get issue trends
        cursor.execute("""
            SELECT issue_type, severity, COUNT(*) 
            FROM validation_issues vi
            JOIN validation_sessions vs ON vi.session_id = vs.session_id
            WHERE vs.timestamp >= ?
            GROUP BY issue_type, severity
            ORDER BY COUNT(*) DESC
        """, (cutoff_date,))
        
        issue_trends = cursor.fetchall()
        
        # Calculate quality distribution
        quality_distribution = {
            "excellent": len([s for s in quality_scores if s >= self.quality_thresholds["excellent"]]),
            "good": len([s for s in quality_scores if self.quality_thresholds["good"] <= s < self.quality_thresholds["excellent"]]),
            "acceptable": len([s for s in quality_scores if self.quality_thresholds["acceptable"] <= s < self.quality_thresholds["good"]]),
            "poor": len([s for s in quality_scores if self.quality_thresholds["poor"] <= s < self.quality_thresholds["acceptable"]]),
            "unacceptable": len([s for s in quality_scores if s < self.quality_thresholds["poor"]])
        }
        
        return {
            "period": f"Last {days} days",
            "summary": {
                "total_validations": trend_data[0],
                "average_accuracy": trend_data[1],
                "average_quality_score": trend_data[2],
                "average_processing_time": trend_data[3],
                "total_issues_found": trend_data[4],
                "total_corrections_applied": trend_data[5],
                "ai_usage_rate": (trend_data[6] / trend_data[0] * 100) if trend_data[0] > 0 else 0
            },
            "quality_distribution": quality_distribution,
            "top_issue_types": [
                {"type": issue[0], "severity": issue[1], "count": issue[2]}
                for issue in issue_trends[:10]
            ],
            "quality_trend": self._calculate_quality_trend(conn, days),
            "recommendations": self._generate_trend_recommendations(trend_data, quality_distribution)
        }
    
    def _get_quality_level(self, score: float) -> str:
        """Get quality level description from score"""
        if score >= self.quality_thresholds["excellent"]:
            return "Excellent"
        elif score >= self.quality_thresholds["good"]:
            return "Good"
        elif score >= self.quality_thresholds["acceptable"]:
            return "Acceptable"
        elif score >= self.quality_thresholds["poor"]:
            return "Poor"
        else:
            return "Unacceptable"
    
    def _calculate_quality_trend(self, conn: sqlite3.Connection, days: int) -> Dict[str, Any]:
        """Calculate quality trend over time"""
        cursor = conn.cursor()
        
        # Get daily averages
        cursor.execute("""
            SELECT DATE(timestamp) as date, AVG(quality_score), COUNT(*)
            FROM validation_sessions 
            WHERE timestamp >= datetime('now', '-{} days')
            GROUP BY DATE(timestamp)
            ORDER BY date
        """.format(days))
        
        daily_data = cursor.fetchall()
        
        if len(daily_data) < 2:
            return {"trend": "insufficient_data"}
        
        # Calculate trend
        first_week_avg = sum(row[1] for row in daily_data[:7]) / min(7, len(daily_data))
        last_week_avg = sum(row[1] for row in daily_data[-7:]) / min(7, len(daily_data))
        
        trend_direction = "improving" if last_week_avg > first_week_avg else "declining"
        trend_magnitude = abs(last_week_avg - first_week_avg)
        
        return {
            "trend": trend_direction,
            "magnitude": trend_magnitude,
            "first_week_average": first_week_avg,
            "last_week_average": last_week_avg,
            "daily_data": [{"date": row[0], "quality": row[1], "validations": row[2]} for row in daily_data]
        }
    
    def _generate_trend_recommendations(self, trend_data: Tuple, quality_distribution: Dict) -> List[str]:
        """Generate recommendations based on trend analysis"""
        recommendations = []
        
        avg_accuracy = trend_data[1] or 0
        avg_quality = trend_data[2] or 0
        ai_usage_rate = (trend_data[6] / trend_data[0] * 100) if trend_data[0] > 0 else 0
        
        # Accuracy recommendations
        if avg_accuracy < 80:
            recommendations.append("🚨 Average accuracy below 80% - Review pattern detection logic")
        elif avg_accuracy < 90:
            recommendations.append("⚠️ Average accuracy below 90% - Consider AI validation for more files")
        
        # Quality recommendations
        if avg_quality < 70:
            recommendations.append("🔧 Overall quality needs improvement - Focus on critical issue resolution")
        
        # AI usage recommendations
        if ai_usage_rate < 20 and avg_quality < 85:
            recommendations.append("🤖 Consider increasing AI validation usage for better quality")
        elif ai_usage_rate > 80:
            recommendations.append("💰 High AI usage detected - Review if pattern validation can be improved")
        
        # Quality distribution recommendations
        unacceptable_rate = quality_distribution.get("unacceptable", 0) / sum(quality_distribution.values()) * 100
        if unacceptable_rate > 20:
            recommendations.append("🚫 High rate of unacceptable quality - Immediate action required")
        
        return recommendations
    
    def export_quality_data(self, output_path: str, days: int = 30) -> str:
        """Export quality data to Excel for analysis"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Export sessions data
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
            sessions_df = pd.read_sql_query("""
                SELECT * FROM validation_sessions 
                WHERE timestamp >= ?
                ORDER BY timestamp DESC
            """, conn, params=(cutoff_date,))
            
            # Export issues data
            issues_df = pd.read_sql_query("""
                SELECT vi.*, vs.timestamp, vs.pdf_file
                FROM validation_issues vi
                JOIN validation_sessions vs ON vi.session_id = vs.session_id
                WHERE vs.timestamp >= ?
                ORDER BY vs.timestamp DESC
            """, conn, params=(cutoff_date,))
            
            # Create Excel file with multiple sheets
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                sessions_df.to_excel(writer, sheet_name='Validation Sessions', index=False)
                issues_df.to_excel(writer, sheet_name='Issues Detail', index=False)
            
            conn.close()
            
            logger.info(f"📊 Quality data exported to: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"❌ Failed to export quality data: {str(e)}")
            return ""

if __name__ == "__main__":
    # Test the quality assurance system
    logging.basicConfig(level=logging.INFO)
    
    qa_system = QualityAssuranceSystem()
    
    # Generate a sample quality report
    report = qa_system.generate_quality_report(days=7)
    print(f"Quality Report: {json.dumps(report, indent=2, default=str)}")
    
    print("✅ Quality Assurance System initialized successfully")