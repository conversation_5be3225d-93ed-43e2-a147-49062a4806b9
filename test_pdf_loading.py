#!/usr/bin/env python3
"""
Test PDF text loading in smart validator
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pdf_loading():
    """Test PDF text loading"""
    print("Testing PDF text loading...")
    
    try:
        from smart_validator import SmartValidationPipeline
        
        # Create validator
        validator = SmartValidationPipeline()
        
        # Test with the actual PDF path from the logs
        pdf_path = r"C:\Users\<USER>\Downloads\pdf to excel\pdfs\KOTHI SALE JULY-2025.pdf"
        
        print(f"[TEST] Testing PDF loading: {pdf_path}")
        print(f"[INFO] PDF exists: {Path(pdf_path).exists()}")
        
        if not Path(pdf_path).exists():
            print("[WARNING] PDF file not found - trying alternative locations")
            
            # Try current directory
            alt_path = "KOTHI SALE JULY-2025.pdf"
            if Path(alt_path).exists():
                pdf_path = alt_path
                print(f"[INFO] Found PDF in current directory: {pdf_path}")
            else:
                print("[ERROR] PDF file not found in any location")
                return False
        
        # Test PDF text loading
        text = validator._load_pdf_text(pdf_path)
        
        if text:
            print(f"[SUCCESS] PDF text loaded: {len(text)} characters")
            print(f"[INFO] Text preview: {text[:200]}...")
            return True
        else:
            print("[ERROR] No text loaded from PDF")
            return False
        
    except Exception as e:
        print(f"[ERROR] Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_pdf_loading()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)