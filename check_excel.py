#!/usr/bin/env python3
"""
Check the content of the Excel file created by AI processing
"""

import sys
import os
import pandas as pd

def check_excel_file():
    """Check the Excel file content"""
    file_path = 'original_KOTHI SALE JULY-2025.xlsx'
    
    if not os.path.exists(file_path):
        print(f"[ERROR] Excel file not found: {file_path}")
        return False
    
    print(f"[INFO] Checking Excel file: {file_path}")
    print(f"[INFO] File size: {os.path.getsize(file_path)} bytes")
    
    try:
        # Read all sheets
        excel_file = pd.ExcelFile(file_path)
        print(f"[INFO] Sheets in file: {excel_file.sheet_names}")
        
        # Check main data sheet
        if 'Main Data' in excel_file.sheet_names:
            df = pd.read_excel(file_path, sheet_name='Main Data')
            print(f"[RESULT] Main Data sheet: {len(df)} records")
            if len(df) > 0:
                print(f"[INFO] Columns: {list(df.columns)}")
                print(f"[INFO] First record: {df.iloc[0].to_dict()}")
            else:
                print("[ERROR] Main Data sheet is empty!")
        else:
            print("[ERROR] No 'Main Data' sheet found!")
        
        # Check other sheets
        for sheet_name in excel_file.sheet_names:
            if sheet_name != 'Main Data':
                df_sheet = pd.read_excel(file_path, sheet_name=sheet_name)
                print(f"[INFO] {sheet_name} sheet: {len(df_sheet)} records")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Error reading Excel file: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_excel_file()
    sys.exit(0 if success else 1)