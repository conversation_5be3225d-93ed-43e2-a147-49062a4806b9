#!/usr/bin/env python3
"""
Test the complete AI validation workflow
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_workflow():
    """Test the complete AI validation workflow"""
    print("Testing AI validation workflow...")
    
    try:
        from enhanced_main import EnhancedPDFConverter
        
        # Test 1: Fast initialization
        print("[TEST 1] Testing fast initialization...")
        import time
        start_time = time.time()
        converter = EnhancedPDFConverter()
        init_time = time.time() - start_time
        print(f"[OK] Enhanced converter initialized in {init_time:.2f}s (should be <5s)")
        
        if init_time > 5:
            print("[WARNING] Initialization took longer than expected")
        
        # Test 2: AI availability check
        print("[TEST 2] Testing AI availability check...")
        ai_available = converter._check_ai_availability()
        print(f"[INFO] AI available: {ai_available}")
        
        if ai_available:
            print("[OK] AI is available for validation")
        else:
            print("[WARNING] AI not available - Ollama may not be running")
        
        # Test 3: Test AI component initialization (without actually running it)
        print("[TEST 3] Testing AI component initialization...")
        if hasattr(converter, '_initialize_ai_components'):
            print("[OK] AI component initialization method exists")
        else:
            print("[ERROR] AI component initialization method missing")
            return False
        
        # Test 4: Test the workflow methods exist
        methods_to_check = [
            'convert_with_validation',
            '_run_original_conversion',
            '_initialize_ai_components',
            '_check_ai_availability'
        ]
        
        for method_name in methods_to_check:
            if hasattr(converter, method_name):
                print(f"[OK] Method {method_name} exists")
            else:
                print(f"[ERROR] Method {method_name} missing")
                return False
        
        print("\n[SUCCESS] AI validation workflow is ready")
        print("[INFO] When you run a conversion with AI enabled:")
        print("  1. Fast initialization (< 5 seconds)")
        print("  2. Standard PDF to Excel conversion")
        print("  3. AI components initialize on first use")
        print("  4. AI validation and correction applied")
        print("  5. Quality assessment and reporting")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] AI workflow test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ai_workflow()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)