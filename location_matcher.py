#!/usr/bin/env python3
"""
Fuzzy Location Matching System for Delhi NCR
Uses comprehensive area database with fuzzy string matching for maximum accuracy
"""

from fuzzywuzzy import fuzz, process
import re
import logging

logger = logging.getLogger(__name__)

class LocationMatcher:
    """Fuzzy matching system for Delhi NCR locations"""
    
    def __init__(self):
        """Initialize the location matcher with comprehensive area database"""
        
        # Comprehensive Delhi NCR Location Database
        self.locations_db = {
            # ========== SOUTH DELHI ==========
            'SOUTH_DELHI': [
                'Adhchini', 'Alaknanda', 'Anand Lok', 'Andrews Ganj', 'Chattarpur',
                'Chirag Delhi', 'Chittaranjan Park', 'CR Park', 'C.R. Park', 'C. R. Park',
                'Defence Colony', 'East of Kailash', 'Friends Colony East', 'Friends Colony West',
                'Friends Colony', 'Gautam Nagar', 'Greater Kailash I', 'Greater Kailash II',
                'Greater Kailash', 'GK-I', 'GK-II', 'GK-1', 'GK-2', 'G.K-1', 'G.K-2',
                'Green Park', 'Gulmohar Park', 'Hauz Khas', 'Hauz Khas Village', 'Hemkunt Colony',
                'Jangpura', 'Jor Bagh', 'Kailash Colony', 'Kalindi Colony', 'Kalkaji', 'Khanpur',
                'Lajpat Nagar', 'Lajpat Nagar-1', 'Lajpat Nagar-2', 'Lajpat Nagar-3', 'Lajpat Nagar-4',
                'Lodi Colony', 'Malviya Nagar', 'Maharani Bagh', 'Mehrauli', 'Neeti Bagh', 'Nehru Place',
                'New Friends Colony', 'Nizamuddin East', 'Nizamuddin West', 'Nizamuddin',
                'Okhla', 'Pamposh Enclave', 'Panchsheel Enclave', 'Panchsheel Park', 'R. K. Puram', 'RK Puram',
                'Safdarjung Enclave', 'Sainik Farm', 'Saket', 'Sarita Vihar', 'Sarojini Nagar',
                'Sarvodaya Enclave', 'Shahpur Jat', 'South Extension', 'South Extension-1',
                'South Extension-2', 'South Ex-1', 'South Ex-2', 'South EX-1', 'South EX-2',
                'Srinivaspuri', 'Uday Park', 'Vasant Kunj'
            ],
            
            # ========== NEW DELHI (CENTRAL) ==========
            'NEW_DELHI': [
                'Barakhamba Road', 'Bhikaji Cama Place', 'Chanakyapuri', 'Diplomatic Enclave',
                'Connaught Place', 'Rajiv Chowk', 'CP', 'Daryaganj', 'Gole Market',
                'INA Colony', 'ITO', 'Janpath', 'Karol Bagh', 'Khan Market',
                'Mandir Marg', 'Minto Road', 'Paharganj', 'Patel Nagar East', 'Patel Nagar West',
                'Patel Nagar South', 'Patel Nagar', 'Pragati Maidan', "President's Estate",
                'Rashtrapati Bhavan', 'Rajendra Nagar', 'Old Rajendra Nagar', 'New Rajendra Nagar',
                'Shankar Market', 'Sundar Nagar'
            ],
            
            # ========== WEST DELHI ==========
            'WEST_DELHI': [
                'Ashok Nagar', 'Dwarka', 'Dwarka Sub-City', 'Hari Nagar', 'Janakpuri',
                'Kirti Nagar', 'Mayapuri', 'Moti Nagar', 'Najafgarh', 'Naraina',
                'Paschim Vihar', 'Punjabi Bagh', 'Rajouri Garden', 'Subhash Nagar',
                'Tagore Garden', 'Tilak Nagar', 'Uttam Nagar', 'Vikas Puri'
            ],
            
            # ========== NORTH DELHI ==========
            'NORTH_DELHI': [
                'Adarsh Nagar', 'Ashok Vihar', 'Azadpur', 'Civil Lines', 'Derawal Nagar',
                'Gujranwala Town', 'Kamla Nagar', 'Kashmere Gate', 'Kingsway Camp',
                'Model Town', 'Mukherjee Nagar', 'Narela', 'Pitampura', 'Rohini',
                'Shalimar Bagh', 'Shastri Nagar', 'Timarpur'
            ],
            
            # ========== EAST DELHI ==========
            'EAST_DELHI': [
                'Anand Vihar', 'Dilshad Garden', 'Gandhi Nagar', 'Geeta Colony',
                'Krishna Nagar', 'Laxmi Nagar', 'Mayur Vihar Phase I', 'Mayur Vihar Phase II',
                'Mayur Vihar Phase III', 'Mayur Vihar', 'Pandav Nagar', 'Patparganj',
                'Preet Vihar', 'Shahdara', 'Vasundhara Enclave', 'Vivek Vihar', 'Yamuna Vihar'
            ],
            
            # ========== GURUGRAM ==========
            'GURUGRAM': [
                'Ambience Island', 'DLF City Phase I', 'DLF City Phase II', 'DLF City Phase III',
                'DLF City Phase IV', 'DLF City Phase V', 'DLF City', 'Golf Course Road',
                'Golf Course Extension', 'MG Road', 'Nirvana Country', 'Palam Vihar',
                'Sohna Road', 'South City I', 'South City II', 'South City',
                'Sushant Lok Phase 1', 'Sushant Lok Phase 2', 'Sushant Lok Phase 3',
                'Sushant Lok', 'Udyog Vihar', 'Gurugram'
            ],
            
            # ========== NOIDA & GREATER NOIDA ==========
            'NOIDA': [
                'Noida', 'Greater Noida', 'Alpha I', 'Alpha II', 'Alpha', 'Beta I', 'Beta II', 'Beta',
                'Chi I', 'Chi II', 'Chi III', 'Chi IV', 'Chi V', 'Chi', 'Delta I', 'Delta II', 'Delta III', 'Delta',
                'Gamma I', 'Gamma II', 'Gamma', 'Greater Noida West', 'Jaypee Greens',
                'Knowledge Park I', 'Knowledge Park II', 'Knowledge Park III', 'Knowledge Park',
                'Omega I', 'Omega II', 'Omega III', 'Omega', 'Pari Chowk',
                'Phi I', 'Phi II', 'Phi III', 'Phi', 'Swarn Nagri', 'Noida Extension'
            ],
            
            # ========== GHAZIABAD ==========
            'GHAZIABAD': [
                'Chander Nagar', 'Crossing Republik', 'Govindpuram', 'Indirapuram',
                'Abhay Khand', 'Gyan Khand', 'Niti Khand', 'Kaushambi', 'Mohan Nagar',
                'Raj Nagar Extension', 'Rajendra Nagar', 'Sahibabad', 'Vaishali', 'Vasundhara'
            ],
            
            # ========== FARIDABAD ==========
            'FARIDABAD': [
                'Ballabgarh', 'Green Valley', 'Neharpar', 'Greater Faridabad',
                'New Industrial Township', 'NIT', 'Old Faridabad', 'Surajkund', 'Faridabad'
            ]
        }
        
        # Create flat list of all locations for fuzzy matching
        self.all_locations = []
        for zone_locations in self.locations_db.values():
            self.all_locations.extend(zone_locations)
        
        # Add common abbreviations and variations
        self.common_variations = {
            'CR': 'Chittaranjan Park',
            'CP': 'Connaught Place',
            'GK': 'Greater Kailash',
            'RK': 'R. K. Puram',
            'DLF': 'DLF City',
            'NFC': 'New Friends Colony',
            'FC': 'Friends Colony',
            'SE': 'South Extension',
            'EOK': 'East of Kailash',
            'HK': 'Hauz Khas',
            'KB': 'Karol Bagh'
        }
        
        logger.info(f"🗺️  Location database initialized with {len(self.all_locations)} locations")
    
    def find_location(self, text: str, confidence_threshold: int = 75) -> dict:
        """
        Find the best matching location using fuzzy matching
        
        Args:
            text: Input text to match against locations
            confidence_threshold: Minimum similarity score (0-100)
            
        Returns:
            dict: {'location': matched_location, 'confidence': score, 'original': original_text}
        """
        if not text or len(text.strip()) < 2:
            return {'location': None, 'confidence': 0, 'original': text}
        
        # Clean the input text
        cleaned_text = self._clean_location_text(text)
        
        # Try exact matches first (case insensitive)
        for location in self.all_locations:
            if cleaned_text.upper() == location.upper():
                return {'location': location, 'confidence': 100, 'original': text}
        
        # Try common abbreviations
        for abbrev, full_name in self.common_variations.items():
            if abbrev.upper() in cleaned_text.upper():
                return {'location': full_name, 'confidence': 95, 'original': text}
        
        # Use fuzzy matching
        best_match = process.extractOne(cleaned_text, self.all_locations, scorer=fuzz.token_sort_ratio)
        
        if best_match and best_match[1] >= confidence_threshold:
            return {
                'location': best_match[0],
                'confidence': best_match[1],
                'original': text
            }
        
        # Try partial matching for complex location names
        best_partial = process.extractOne(cleaned_text, self.all_locations, scorer=fuzz.partial_ratio)
        
        if best_partial and best_partial[1] >= confidence_threshold + 10:  # Higher threshold for partial matches
            return {
                'location': best_partial[0],
                'confidence': best_partial[1] - 10,  # Reduce confidence for partial matches
                'original': text
            }
        
        return {'location': None, 'confidence': 0, 'original': text}
    
    def _clean_location_text(self, text: str) -> str:
        """Clean location text for better matching"""
        # Remove common suffixes that might interfere
        text = re.sub(r'[-–\s]*$', '', text.strip())
        
        # Remove obvious property details that might interfere with location matching
        property_suffixes = ['GF', 'FF', 'SF', 'TF', 'BMT', 'LGF', 'UGF', 'CRNR', 'CORNER', 'NEW', 'OLD', 'LIFT', 'HALL']
        for suffix in property_suffixes:
            # Remove if it appears in combination with other property indicators
            if suffix in text.upper() and any(indicator in text.upper() for indicator in ['GF', 'FF', 'CRNR', 'FT']):
                return ''  # If it looks like property data, return empty to avoid false matches
        
        # Remove property-related keywords at the end
        property_keywords = ['BLOCK', 'BLK', 'SECTOR', 'SEC', 'PHASE', 'MKT', 'MARKET']
        for keyword in property_keywords:
            # Only remove if it's at the end and followed by numbers/letters
            text = re.sub(rf'\s+{keyword}[-\s]*[A-Z0-9]*$', '', text, flags=re.IGNORECASE)
        
        return text.strip()
    
    def is_valid_location(self, text: str, confidence_threshold: int = 70) -> bool:
        """Check if text is a valid location with given confidence"""
        result = self.find_location(text, confidence_threshold)
        return result['location'] is not None
    
    def get_location_suggestions(self, text: str, limit: int = 5) -> list:
        """Get multiple location suggestions for ambiguous text"""
        cleaned_text = self._clean_location_text(text)
        matches = process.extract(cleaned_text, self.all_locations, limit=limit, scorer=fuzz.token_sort_ratio)
        
        return [{'location': match[0], 'confidence': match[1], 'original': text} for match in matches]
    
    def enhance_location_with_context(self, detected_location: str, previous_location: str = None) -> str:
        """Enhance detected location using context from previous location"""
        result = self.find_location(detected_location)
        
        if result['location']:
            return result['location']
        
        # If no direct match and we have previous location, try context-based matching
        if previous_location:
            # Try combining with previous location zone
            for zone, locations in self.locations_db.items():
                if previous_location in locations:
                    # Look for similar locations in the same zone
                    zone_matches = process.extract(detected_location, locations, limit=3, scorer=fuzz.token_sort_ratio)
                    if zone_matches and zone_matches[0][1] >= 70:
                        return zone_matches[0][0]
        
        return detected_location  # Return original if no enhancement possible

# Create global instance
location_matcher = LocationMatcher()

def find_best_location_match(text: str, confidence_threshold: int = 75) -> dict:
    """Convenience function for location matching"""
    return location_matcher.find_location(text, confidence_threshold)

def is_known_location(text: str, confidence_threshold: int = 70) -> bool:
    """Convenience function to check if text is a known location"""
    return location_matcher.is_valid_location(text, confidence_threshold) 