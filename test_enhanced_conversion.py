#!/usr/bin/env python3
"""
Test the enhanced conversion process
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_conversion():
    """Test the enhanced conversion process"""
    print("Testing enhanced conversion process...")
    
    try:
        from enhanced_main import EnhancedPDFConverter
        
        # Check if test PDF exists
        test_pdf = Path("KOTHI SALE JULY-2025.pdf")
        if not test_pdf.exists():
            print(f"[WARNING] Test PDF not found: {test_pdf}")
            print("[INFO] Testing with component imports only")
        
        # Test 1: Can we create the converter?
        print("[TEST 1] Creating EnhancedPDFConverter...")
        converter = EnhancedPDFConverter()
        print("[OK] EnhancedPDFConverter created successfully")
        
        # Test 2: Test individual components
        print("[TEST 2] Testing original conversion components...")
        
        # Test PDF parser
        from pdf_parser import PDFParser
        parser = PDFParser()
        print("[OK] PDFParser imported and created")
        
        # Test data processor
        from data_processor import DataProcessor
        processor = DataProcessor()
        print("[OK] DataProcessor imported and created")
        
        # Test Excel generator
        from excel_generator import ExcelGenerator
        generator = ExcelGenerator()
        print("[OK] ExcelGenerator imported and created")
        
        # Test 3: Test the conversion method exists
        if hasattr(converter, 'convert_with_validation'):
            print("[OK] convert_with_validation method exists")
        else:
            print("[ERROR] convert_with_validation method missing")
            return False
        
        # Test 4: Test _run_original_conversion method
        if hasattr(converter, '_run_original_conversion'):
            print("[OK] _run_original_conversion method exists")
        else:
            print("[ERROR] _run_original_conversion method missing")
            return False
        
        print("\n[SUCCESS] Enhanced conversion components working")
        return True
        
    except Exception as e:
        print(f"[ERROR] Enhanced conversion test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_conversion()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)