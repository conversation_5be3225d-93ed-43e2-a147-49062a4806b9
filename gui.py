"""
GUI Module
Creates a beautiful and intuitive user interface for the PDF to Excel converter
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import glob
import datetime
from typing import List, Callable, Optional
import logging
from config import GUI_CONFIG

# Configure logging
logger = logging.getLogger(__name__)

class PDFConverterGUI:
    """
    Beautiful GUI for PDF to Excel Converter
    """
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        self.selected_files = []
        self.output_path = ""
        self.conversion_callback: Optional[Callable] = None
        
    def setup_window(self):
        """Setup main window properties"""
        self.root.title(GUI_CONFIG['window_title'])
        self.root.geometry(GUI_CONFIG['window_size'])
        self.root.resizable(True, True)
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_reqwidth() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_reqheight() // 2)
        self.root.geometry(f"+{x}+{y}")
        
        # Set icon if available
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass  # Icon file not found, continue without it
    
    def setup_styles(self):
        """Setup modern styling"""
        style = ttk.Style()
        
        # Configure styles with modern colors
        style.configure('Title.TLabel', 
                       font=('Segoe UI', 20, 'bold'),
                       foreground=GUI_CONFIG['theme_color'])
        
        style.configure('Subtitle.TLabel',
                       font=('Segoe UI', 12),
                       foreground='#666666')
        
        style.configure('Header.TLabel',
                       font=('Segoe UI', 11, 'bold'),
                       foreground='#333333')
        
        style.configure('Action.TButton',
                       font=('Segoe UI', 10, 'bold'),
                       padding=(20, 10))
        
        style.configure('Success.TLabel',
                       font=('Segoe UI', 10),
                       foreground=GUI_CONFIG['success_color'])
        
        style.configure('Error.TLabel',
                       font=('Segoe UI', 10),
                       foreground=GUI_CONFIG['error_color'])
    
    def create_widgets(self):
        """Create and arrange all GUI widgets"""
        # Main container with padding
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky="nsew")
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title Section
        self.create_title_section(main_frame, 0)
        
        # File Selection Section
        self.create_file_section(main_frame, 1)
        
        # Output Settings Section
        self.create_output_section(main_frame, 2)
        
        # Progress Section
        self.create_progress_section(main_frame, 3)
        
        # Action Buttons Section
        self.create_action_section(main_frame, 4)
        
        # Status Section
        self.create_status_section(main_frame, 5)
    
    def create_title_section(self, parent, row):
        """Create title section"""
        title_frame = ttk.Frame(parent)
        title_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 30))
        
        # Main title
        title_label = ttk.Label(title_frame, 
                               text="PDF to Excel Converter",
                               style='Title.TLabel')
        title_label.grid(row=0, column=0, sticky=tk.W)
        
        # Subtitle
        subtitle_label = ttk.Label(title_frame,
                                  text="Convert real estate PDF listings to structured Excel format",
                                  style='Subtitle.TLabel')
        subtitle_label.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
    
    def create_file_section(self, parent, row):
        """Create file selection section"""
        # Section header
        files_header = ttk.Label(parent, text="📁 Select PDF Files", style='Header.TLabel')
        files_header.grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        # File selection frame
        file_frame = ttk.Frame(parent)
        file_frame.grid(row=row+1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        file_frame.columnconfigure(1, weight=1)
        
        # Buttons frame
        button_frame = ttk.Frame(file_frame)
        button_frame.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        # Select files button
        self.select_files_btn = ttk.Button(button_frame,
                                          text="Select PDF Files",
                                          command=self.select_files)
        self.select_files_btn.grid(row=0, column=0, padx=(0, 5))
        
        # Select folder button
        self.select_folder_btn = ttk.Button(button_frame,
                                           text="Select Folder",
                                           command=self.select_folder)
        self.select_folder_btn.grid(row=0, column=1, padx=(5, 0))
        
        # File list with scrollbar
        list_frame = ttk.Frame(file_frame)
        list_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Listbox with scrollbar
        self.file_listbox = tk.Listbox(list_frame, height=6, selectmode=tk.EXTENDED)
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.file_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Clear selection button
        clear_btn = ttk.Button(file_frame, text="Clear Selection", command=self.clear_files)
        clear_btn.grid(row=1, column=0, columnspan=2, pady=(10, 0))
    
    def create_output_section(self, parent, row):
        """Create output settings section"""
        # Section header
        output_header = ttk.Label(parent, text="💾 Output Settings", style='Header.TLabel')
        output_header.grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        
        # Output frame
        output_frame = ttk.Frame(parent)
        output_frame.grid(row=row+1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        output_frame.columnconfigure(1, weight=1)
        
        # Output path label
        ttk.Label(output_frame, text="Save to:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        # Output path entry
        self.output_var = tk.StringVar(value="Real_Estate_Data_Converted.xlsx")
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_var, width=50)
        self.output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # Browse button
        browse_btn = ttk.Button(output_frame, text="Browse", command=self.browse_output)
        browse_btn.grid(row=0, column=2)
    
    def create_progress_section(self, parent, row):
        """Create progress tracking section"""
        # Progress frame
        progress_frame = ttk.Frame(parent)
        progress_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        progress_frame.columnconfigure(0, weight=1)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, 
                                           variable=self.progress_var,
                                           maximum=100,
                                           length=400,
                                           mode='determinate')
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Progress label
        self.progress_label = ttk.Label(progress_frame, text="Ready to convert")
        self.progress_label.grid(row=1, column=0, sticky=tk.W)
    
    def create_action_section(self, parent, row):
        """Create action buttons section"""
        action_frame = ttk.Frame(parent)
        action_frame.grid(row=row, column=0, columnspan=2, pady=(0, 20))
        
        # Convert button
        self.convert_btn = ttk.Button(action_frame,
                                     text="🚀 Convert to Excel",
                                     style='Action.TButton',
                                     command=self.start_conversion)
        self.convert_btn.grid(row=0, column=0, padx=(0, 10))
        
        # Cancel button
        self.cancel_btn = ttk.Button(action_frame,
                                    text="❌ Cancel",
                                    command=self.cancel_conversion,
                                    state=tk.DISABLED)
        self.cancel_btn.grid(row=0, column=1)
    
    def create_status_section(self, parent, row):
        """Create status display section"""
        # Status frame with border
        status_frame = ttk.LabelFrame(parent, text="Status", padding="10")
        status_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(0, weight=1)
        
        # Status text with scrollbar
        text_frame = ttk.Frame(status_frame)
        text_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        
        self.status_text = tk.Text(text_frame, height=8, width=70, wrap=tk.WORD)
        status_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=status_scrollbar.set)
        
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        status_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Initial status message
        self.add_status_message("Welcome! Select PDF files to begin conversion.")
    
    def select_files(self):
        """Open file dialog to select PDF files"""
        filetypes = [
            ('PDF files', '*.pdf'),
            ('All files', '*.*')
        ]
        
        files = filedialog.askopenfilenames(
            title="Select PDF Files",
            filetypes=filetypes,
            initialdir=os.getcwd()
        )
        
        if files:
            self.selected_files = list(files)
            self.update_file_list()
            self.add_status_message(f"Selected {len(files)} PDF file(s)")
    
    def select_folder(self):
        """Select folder and find all PDF files"""
        folder = filedialog.askdirectory(
            title="Select Folder with PDF Files",
            initialdir=os.getcwd()
        )
        
        if folder:
            # Find all PDF files in the folder
            pdf_files = glob.glob(os.path.join(folder, "*.pdf"))
            
            if pdf_files:
                self.selected_files = pdf_files
                self.update_file_list()
                self.add_status_message(f"Found {len(pdf_files)} PDF file(s) in folder: {folder}")
            else:
                messagebox.showwarning("No PDF Files", "No PDF files found in the selected folder.")
    
    def clear_files(self):
        """Clear selected files"""
        self.selected_files = []
        self.update_file_list()
        self.add_status_message("File selection cleared")
    
    def update_file_list(self):
        """Update the file listbox"""
        self.file_listbox.delete(0, tk.END)
        for file_path in self.selected_files:
            filename = os.path.basename(file_path)
            self.file_listbox.insert(tk.END, filename)
    
    def browse_output(self):
        """Browse for output file location"""
        filename = filedialog.asksaveasfilename(
            title="Save Excel File As",
            defaultextension=".xlsx",
            filetypes=[
                ('Excel files', '*.xlsx'),
                ('All files', '*.*')
            ],
            initialfile="Real_Estate_Data_Converted.xlsx"
        )
        
        if filename:
            self.output_var.set(filename)
    
    def add_status_message(self, message: str, message_type: str = "info"):
        """Add message to status text"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # Color based on message type
        if message_type == "error":
            color = GUI_CONFIG['error_color']
        elif message_type == "success":
            color = GUI_CONFIG['success_color']
        else:
            color = "#333333"
        
        # Configure text tags for colors
        self.status_text.tag_configure("error", foreground=GUI_CONFIG['error_color'])
        self.status_text.tag_configure("success", foreground=GUI_CONFIG['success_color'])
        self.status_text.tag_configure("info", foreground="#333333")
        
        # Insert message with timestamp
        self.status_text.insert(tk.END, f"[{timestamp}] {message}\n", message_type)
        self.status_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_progress(self, current: int, total: int, message: str = ""):
        """Update progress bar and message"""
        if total > 0:
            progress_percent = (current / total) * 100
            self.progress_var.set(progress_percent)
        
        if message:
            self.progress_label.config(text=message)
        
        self.root.update_idletasks()
    
    def start_conversion(self):
        """Start the conversion process"""
        if not self.selected_files:
            messagebox.showwarning("No Files Selected", "Please select PDF files to convert.")
            return
        
        output_path = self.output_var.get().strip()
        if not output_path:
            messagebox.showwarning("No Output Path", "Please specify an output file path.")
            return
        
        # Disable convert button and enable cancel
        self.convert_btn.config(state=tk.DISABLED)
        self.cancel_btn.config(state=tk.NORMAL)
        
        # Reset progress
        self.progress_var.set(0)
        self.add_status_message("Starting conversion process...", "info")
        
        # Start conversion in separate thread
        self.conversion_thread = threading.Thread(
            target=self._run_conversion,
            args=(self.selected_files, output_path),
            daemon=True
        )
        self.conversion_thread.start()
    
    def _run_conversion(self, files: List[str], output_path: str):
        """Run conversion in separate thread"""
        try:
            if self.conversion_callback:
                self.conversion_callback(files, output_path, self.update_progress, self.add_status_message)
            else:
                self.add_status_message("No conversion callback set", "error")
        except Exception as e:
            self.add_status_message(f"Conversion failed: {str(e)}", "error")
        finally:
            # Re-enable convert button and disable cancel
            self.root.after(0, self._reset_buttons)
    
    def _reset_buttons(self):
        """Reset button states (called from main thread)"""
        self.convert_btn.config(state=tk.NORMAL)
        self.cancel_btn.config(state=tk.DISABLED)
    
    def cancel_conversion(self):
        """Cancel the conversion process"""
        self.add_status_message("Conversion cancelled by user", "error")
        # Note: Actual cancellation would need to be implemented in the conversion logic
        self._reset_buttons()
    
    def set_conversion_callback(self, callback: Callable):
        """Set the callback function for conversion"""
        self.conversion_callback = callback
    
    def run(self):
        """Start the GUI event loop"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("GUI closed by user")

# Example usage
if __name__ == "__main__":
    app = PDFConverterGUI()
    
    def dummy_conversion(files, output_path, progress_callback, status_callback):
        """Dummy conversion function for testing"""
        import time
        
        total_files = len(files)
        for i, file in enumerate(files, 1):
            status_callback(f"Processing: {os.path.basename(file)}")
            progress_callback(i, total_files, f"Processing file {i} of {total_files}")
            time.sleep(1)  # Simulate processing time
        
        status_callback("Conversion completed successfully!", "success")
        progress_callback(total_files, total_files, "Conversion complete")
    
    app.set_conversion_callback(dummy_conversion)
    app.run() 