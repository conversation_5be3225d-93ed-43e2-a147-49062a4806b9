"""
PDF Parser Module
Handles extraction of text content from PDF files using pdfplumber with comprehensive logging
"""

import pdfplumber
import os
import logging
from typing import List, Dict, Optional, Any
from config import PROCESSING_CONFIG

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PDFParser:
    """
    A robust PDF parser for extracting text content from real estate PDF files with detailed logging
    """
    
    def __init__(self):
        self.max_file_size = PROCESSING_CONFIG['max_file_size_mb'] * 1024 * 1024  # Convert to bytes
        self.processed_files = []
        self.failed_files = []
        self.extraction_stats = {}
    
    def validate_pdf_file(self, file_path: str) -> bool:
        """
        Validate if the file is a proper PDF and within size limits
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            bool: True if file is valid, False otherwise
        """
        try:
            # Check if file exists
            if not os.path.exists(file_path):
                logger.error(f"❌ VALIDATION FAILED: File not found: {file_path}")
                return False
            
            # Check file extension
            if not file_path.lower().endswith('.pdf'):
                logger.error(f"❌ VALIDATION FAILED: File is not a PDF: {file_path}")
                return False
            
            # Check file size
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)
            logger.info(f"📄 FILE SIZE: {os.path.basename(file_path)} - {file_size_mb:.2f} MB")
            
            if file_size > self.max_file_size:
                logger.error(f"❌ VALIDATION FAILED: File too large: {file_path} ({file_size_mb:.2f} MB)")
                return False
            
            # Try to open with pdfplumber to validate
            with pdfplumber.open(file_path) as pdf:
                page_count = len(pdf.pages)
                if page_count == 0:
                    logger.error(f"❌ VALIDATION FAILED: PDF has no pages: {file_path}")
                    return False
                else:
                    logger.info(f"✅ VALIDATION SUCCESS: {os.path.basename(file_path)} - {page_count} pages")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ VALIDATION ERROR: {file_path} - {str(e)}")
            return False
    
    def extract_text_from_pdf(self, file_path: str) -> Optional[str]:
        """
        Extract all text content from a PDF file with comprehensive logging
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            str: Extracted text content or None if extraction fails
        """
        try:
            if not self.validate_pdf_file(file_path):
                return None
            
            filename = os.path.basename(file_path)
            extracted_text = ""
            page_stats = []
            total_chars_raw = 0
            total_chars_cleaned = 0
            
            logger.info(f"🔄 STARTING TEXT EXTRACTION: {filename}")
            
            with pdfplumber.open(file_path) as pdf:
                total_pages = len(pdf.pages)
                logger.info(f"📊 PROCESSING: {filename} ({total_pages} pages)")
                
                for page_num, page in enumerate(pdf.pages, 1):
                    try:
                        logger.debug(f"  📄 Processing page {page_num}/{total_pages}")
                        
                        # Extract text from the page
                        page_text = page.extract_text()
                        
                        if page_text:
                            raw_char_count = len(page_text)
                            raw_line_count = len(page_text.split('\n'))
                            
                            logger.debug(f"    📝 Raw text extracted: {raw_char_count} chars, {raw_line_count} lines")
                            
                            # Clean and process the text
                            cleaned_text = self.clean_extracted_text(page_text)
                            cleaned_char_count = len(cleaned_text)
                            cleaned_line_count = len(cleaned_text.split('\n'))
                            
                            logger.debug(f"    🧹 After cleaning: {cleaned_char_count} chars, {cleaned_line_count} lines")
                            logger.debug(f"    📉 Data reduction: {raw_char_count - cleaned_char_count} chars removed, {raw_line_count - cleaned_line_count} lines removed")
                            
                            extracted_text += cleaned_text + "\n"
                            total_chars_raw += raw_char_count
                            total_chars_cleaned += cleaned_char_count
                            
                            page_stats.append({
                                'page': page_num,
                                'raw_chars': raw_char_count,
                                'cleaned_chars': cleaned_char_count,
                                'raw_lines': raw_line_count,
                                'cleaned_lines': cleaned_line_count
                            })
                        else:
                            logger.warning(f"    ⚠️  Page {page_num}: No text extracted")
                            page_stats.append({
                                'page': page_num,
                                'raw_chars': 0,
                                'cleaned_chars': 0,
                                'raw_lines': 0,
                                'cleaned_lines': 0
                            })
                        
                    except Exception as e:
                        logger.error(f"    ❌ Error processing page {page_num} in {filename}: {str(e)}")
                        continue
            
            # Store extraction statistics
            self.extraction_stats[file_path] = {
                'total_pages': total_pages,
                'raw_chars': total_chars_raw,
                'cleaned_chars': total_chars_cleaned,
                'data_loss_chars': total_chars_raw - total_chars_cleaned,
                'data_loss_percentage': ((total_chars_raw - total_chars_cleaned) / total_chars_raw * 100) if total_chars_raw > 0 else 0,
                'page_stats': page_stats
            }
            
            if extracted_text.strip():
                self.processed_files.append(file_path)
                logger.info(f"✅ EXTRACTION SUCCESS: {filename}")
                logger.info(f"   📊 Total: {total_chars_raw} → {total_chars_cleaned} chars ({self.extraction_stats[file_path]['data_loss_percentage']:.1f}% reduction)")
                
                # Sample first few lines for verification
                lines = extracted_text.strip().split('\n')[:5]
                logger.debug(f"   📋 First 5 lines preview:")
                for i, line in enumerate(lines, 1):
                    logger.debug(f"      {i}: {line[:100]}{'...' if len(line) > 100 else ''}")
                
                return extracted_text
            else:
                logger.error(f"❌ EXTRACTION FAILED: No text extracted from {filename}")
                self.failed_files.append(file_path)
                return None
                
        except Exception as e:
            logger.error(f"❌ EXTRACTION ERROR: {filename} - {str(e)}")
            self.failed_files.append(file_path)
            return None
    
    def clean_extracted_text(self, text: str) -> str:
        """
        Clean and normalize extracted text with detailed logging
        
        Args:
            text: Raw extracted text
            
        Returns:
            str: Cleaned text
        """
        if not text:
            logger.debug("      🔍 No text to clean")
            return ""
        
        # Log initial state
        initial_lines = text.split('\n')
        initial_line_count = len(initial_lines)
        empty_lines_initial = sum(1 for line in initial_lines if not line.strip())
        
        logger.debug(f"      🔍 Cleaning: {initial_line_count} lines ({empty_lines_initial} empty)")
        
        # Remove excessive whitespace
        cleaned_lines = []
        skipped_lines = 0
        
        for line in initial_lines:
            # Strip whitespace and skip empty lines
            line = line.strip()
            if line:
                # Remove excessive spaces within the line
                original_line = line
                line = ' '.join(line.split())
                if len(line) != len(original_line):
                    logger.debug(f"        🔧 Compressed line: '{original_line[:50]}...' → '{line[:50]}...'")
                cleaned_lines.append(line)
            else:
                skipped_lines += 1
        
        cleaned_text = '\n'.join(cleaned_lines)
        
        logger.debug(f"      ✨ Cleaning result: {len(cleaned_lines)} lines kept, {skipped_lines} empty lines removed")
        
        # Check for potential data loss patterns
        if skipped_lines > initial_line_count * 0.5:
            logger.warning(f"      ⚠️  HIGH DATA LOSS: {skipped_lines}/{initial_line_count} lines removed ({skipped_lines/initial_line_count*100:.1f}%)")
        
        return cleaned_text
    
    def extract_from_multiple_pdfs(self, pdf_files: List[str], progress_callback=None) -> Dict[str, str]:
        """
        Extract text from multiple PDF files with progress tracking and comprehensive logging
        
        Args:
            pdf_files: List of PDF file paths
            progress_callback: Optional callback function for progress updates
            
        Returns:
            dict: Dictionary mapping file paths to extracted text
        """
        results = {}
        total_files = len(pdf_files)
        
        logger.info(f"🚀 BATCH EXTRACTION STARTED: {total_files} files")
        logger.info("=" * 60)
        
        for i, file_path in enumerate(pdf_files, 1):
            try:
                logger.info(f"📁 PROCESSING FILE {i}/{total_files}: {os.path.basename(file_path)}")
                
                # Update progress
                if progress_callback:
                    progress_callback(i, total_files, os.path.basename(file_path))
                
                # Extract text
                extracted_text = self.extract_text_from_pdf(file_path)
                
                if extracted_text:
                    results[file_path] = extracted_text
                    logger.info(f"✅ File {i} completed successfully")
                else:
                    logger.error(f"❌ File {i} failed")
                    
                logger.info("-" * 40)
                    
            except Exception as e:
                logger.error(f"❌ CRITICAL ERROR processing file {file_path}: {str(e)}")
                continue
        
        # Final summary
        successful = len(results)
        failed = len(self.failed_files)
        
        logger.info("=" * 60)
        logger.info(f"🏁 BATCH EXTRACTION COMPLETED")
        logger.info(f"   ✅ Successful: {successful}")
        logger.info(f"   ❌ Failed: {failed}")
        logger.info(f"   📊 Success rate: {successful/total_files*100:.1f}%")
        
        if self.failed_files:
            logger.warning(f"   ⚠️  Failed files:")
            for failed_file in self.failed_files:
                logger.warning(f"      - {os.path.basename(failed_file)}")
        
        # Summary of extraction stats
        if self.extraction_stats:
            total_raw_chars = sum(stats['raw_chars'] for stats in self.extraction_stats.values())
            total_cleaned_chars = sum(stats['cleaned_chars'] for stats in self.extraction_stats.values())
            avg_data_loss = sum(stats['data_loss_percentage'] for stats in self.extraction_stats.values()) / len(self.extraction_stats)
            
            logger.info(f"   📈 Text extraction summary:")
            logger.info(f"      Raw characters: {total_raw_chars:,}")
            logger.info(f"      Cleaned characters: {total_cleaned_chars:,}")
            logger.info(f"      Average data reduction: {avg_data_loss:.1f}%")
        
        return results
    
    def get_processing_stats(self) -> Dict[str, int]:
        """
        Get statistics about the processing
        
        Returns:
            dict: Processing statistics
        """
        return {
            'successful': len(self.processed_files),
            'failed': len(self.failed_files),
            'total': len(self.processed_files) + len(self.failed_files)
        }
    
    def get_extraction_stats(self) -> Dict[str, Any]:
        """
        Get detailed extraction statistics
        
        Returns:
            dict: Detailed extraction statistics
        """
        return self.extraction_stats.copy()
    
    def get_failed_files(self) -> List[str]:
        """
        Get list of files that failed to process
        
        Returns:
            list: List of failed file paths
        """
        return self.failed_files.copy()

# Example usage and testing
if __name__ == "__main__":
    parser = PDFParser()
    
    # Test with a single file
    test_file = "pdfs/KOTHI SALE JUNE-2025.pdf"
    if os.path.exists(test_file):
        result = parser.extract_text_from_pdf(test_file)
        if result:
            print(f"Extracted {len(result)} characters from {test_file}")
            print("First 500 characters:")
            print(result[:500])
        else:
            print("Failed to extract text")
    else:
        print("Test file not found") 