#!/usr/bin/env python3
"""
Debug the conversion process step by step
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_conversion():
    """Debug the conversion process step by step"""
    print("Debugging conversion process...")
    
    # Find the PDF file
    pdf_file = Path("KOTHI SALE JULY-2025.pdf")
    if not pdf_file.exists():
        print(f"[ERROR] PDF file not found: {pdf_file}")
        return False
    
    print(f"[INFO] Found PDF: {pdf_file} ({pdf_file.stat().st_size} bytes)")
    
    try:
        # Step 1: Test PDF parsing
        print("\n=== STEP 1: PDF PARSING ===")
        from pdf_parser import PDFParser
        parser = PDFParser()
        
        print(f"[TEST] Extracting text from: {pdf_file}")
        extracted_data = parser.extract_from_multiple_pdfs([str(pdf_file)])
        
        print(f"[RESULT] Extracted data: {len(extracted_data)} files")
        if extracted_data:
            for file_path, text in extracted_data.items():
                print(f"  File: {Path(file_path).name}")
                print(f"  Text length: {len(text)} characters")
                print(f"  Text preview: {text[:300]}...")
                print(f"  Text contains 'KOTHI': {'KOTHI' in text.upper()}")
                print(f"  Text contains property codes: {any(c in text for c in ['A-', 'B-', 'G-', 'H-'])}")
        else:
            print("[ERROR] No text extracted from PDF")
            return False
        
        # Step 2: Test data processing
        print("\n=== STEP 2: DATA PROCESSING ===")
        from data_processor import DataProcessor
        processor = DataProcessor()
        processor.set_debug_mode(True)
        
        print("[TEST] Processing extracted text into records...")
        records = processor.process_multiple_files(extracted_data)
        
        print(f"[RESULT] Processed records: {len(records)} records")
        if not records.empty:
            print(f"  Columns: {list(records.columns)}")
            print(f"  First few records:")
            for i, (_, record) in enumerate(records.head(3).iterrows()):
                print(f"    Record {i+1}: {record.to_dict()}")
        else:
            print("[ERROR] No records extracted from text")
            
            # Get debug information
            debug_summary = processor.get_debug_summary()
            print(f"[DEBUG] Debug summary: {debug_summary}")
            
            return False
        
        # Step 3: Test Excel generation
        print("\n=== STEP 3: EXCEL GENERATION ===")
        from excel_generator import ExcelGenerator
        generator = ExcelGenerator()
        
        output_path = "debug_test_output.xlsx"
        print(f"[TEST] Creating Excel file: {output_path}")
        
        result_path = generator.create_excel_file(records, output_path)
        
        if Path(result_path).exists():
            file_size = Path(result_path).stat().st_size
            print(f"[RESULT] Excel file created: {result_path} ({file_size} bytes)")
            print("[SUCCESS] All conversion steps working!")
            return True
        else:
            print("[ERROR] Excel file not created")
            return False
        
    except Exception as e:
        print(f"[ERROR] Debug failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_conversion()
    print(f"\nDebug {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)