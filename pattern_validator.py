"""
Pattern-Based Validator for Property Data
Handles 80% of validation issues using intelligent pattern matching
No AI required - fast and free
"""

import re
import json
import logging
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class ValidationIssue:
    """Structure for validation issues"""
    issue_type: str
    severity: str  # 'critical', 'high', 'medium', 'low'
    description: str
    line_number: Optional[int] = None
    expected_value: Optional[str] = None
    actual_value: Optional[str] = None
    suggested_fix: Optional[Dict] = None
    confidence: float = 1.0

@dataclass
class ValidationReport:
    """Complete validation report"""
    total_pdf_records: int
    total_excel_records: int
    match_rate: float
    issues: List[ValidationIssue]
    statistics: Dict[str, Any]
    recommendations: List[str]

class PatternValidator:
    """
    Intelligent pattern-based validator for property data
    Uses rule-based logic to identify and fix common issues
    """
    
    def __init__(self):
        # Property code patterns
        self.property_code_patterns = [
            r'\b([A-Z]{1,3}[-/]?\d+[A-Z]*)\b',  # A-123, B-456, G50, etc.
            r'\b(\d+[A-Z]{1,3})\b',             # 123A, 456B, etc.
        ]
        
        # Size patterns
        self.size_patterns = [
            r'(\d+(?:\.\d+)?)\s*Y(?:ARDS?)?',   # 200Y, 300 YARDS
            r'(\d+(?:\.\d+)?)\s*SQ\.?\s*(?:FT|FEET)', # 1000 SQ FT
            r'(\d+(?:\.\d+)?)\s*ACRES?',        # 2 ACRES
            r'(\d+(?:\.\d+)?)\s*MARLA',         # 5 MARLA
        ]
        
        # Location patterns for Indian real estate
        self.location_patterns = [
            r'\b(EAST OF KAILASH|MAHARANI BAGH|DEFENCE COLONY)\b',
            r'\b(KALINDI COLONY|BENGALI MARKET|HEMKUNT COLONY)\b',
            r'\b(PANCHSHEEL PARK|SAFDARJUNG ENCLAVE|SOUTH EX-?\d*)\b',
            r'\b(FRIENDS COLONY (?:EAST|WEST)|NEW FRIENDS COLONY)\b',
            r'\b(GREEN PARK|HAUZ KHAS|LAJPAT NAGAR-?\d*)\b',
            r'\b(VASANT VIHAR|VASANT KUNJ|NIZAMUDDIN (?:EAST|WEST))\b',
            r'\b(C\.?\s*R\.?\s*PARK|KAROL BAGH|SHIVALIK)\b',
        ]
        
        # Contact patterns
        self.contact_patterns = [
            r'\(([A-Z][A-Z\s\.\'\']+?)\s+(?:[A-Za-z\'\s]*)?\s*(\d{10,})',  # (NAME PHONE)
            r'([A-Z][A-Z\s\.\'\']+?)\s+(\d{10,})',  # NAME PHONE
        ]
        
        # Property type indicators
        self.property_type_indicators = {
            'kothi': r'\bKOTHI\b',
            'duplex': r'\bDUPLEX\b',
            'bungalow': r'\bBUNGALOW?\b',
            'plot': r'\bPLOT\b',
            'house': r'\b(?:HOUSE|OLD HOUSE)\b',
            'shop': r'\bSHOP\b',
            'flat': r'\bFLAT\b',
        }
        
        logger.info("🎯 Pattern Validator initialized with comprehensive rule sets")
    
    def extract_pdf_records(self, pdf_text: str) -> List[Dict[str, Any]]:
        """
        Extract property records from PDF text using pattern matching
        
        Args:
            pdf_text: Raw PDF text
            
        Returns:
            List of extracted property records
        """
        lines = pdf_text.split('\n')
        records = []
        current_location = ""
        
        logger.info(f"🔍 Extracting records from {len(lines)} PDF lines...")
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or len(line) < 5:
                continue
            
            # Check if this is a location header
            location = self._extract_location(line)
            if location:
                current_location = location
                logger.debug(f"📍 Location found at line {line_num}: {location}")
                continue
            
            # Check if this is a property record
            property_record = self._extract_property_record(line, current_location, line_num)
            if property_record:
                records.append(property_record)
                logger.debug(f"🏠 Property found at line {line_num}: {property_record.get('property_code', 'N/A')}")
        
        logger.info(f"✅ Extracted {len(records)} records from PDF")
        return records
    
    def validate_against_excel(self, pdf_records: List[Dict], excel_records: List[Dict]) -> ValidationReport:
        """
        Compare PDF records against Excel records and identify issues
        
        Args:
            pdf_records: Records extracted from PDF
            excel_records: Records from Excel file
            
        Returns:
            Comprehensive validation report
        """
        logger.info(f"🔄 Validating {len(pdf_records)} PDF records against {len(excel_records)} Excel records...")
        
        issues = []
        
        # 1. Find missing records
        missing_issues = self._find_missing_records(pdf_records, excel_records)
        issues.extend(missing_issues)
        
        # 2. Find location mismatches
        location_issues = self._find_location_mismatches(pdf_records, excel_records)
        issues.extend(location_issues)
        
        # 3. Find property code errors
        property_code_issues = self._find_property_code_errors(pdf_records, excel_records)
        issues.extend(property_code_issues)
        
        # 4. Find size discrepancies
        size_issues = self._find_size_discrepancies(pdf_records, excel_records)
        issues.extend(size_issues)
        
        # 5. Find contact information issues
        contact_issues = self._find_contact_issues(pdf_records, excel_records)
        issues.extend(contact_issues)
        
        # Calculate statistics
        match_rate = self._calculate_match_rate(pdf_records, excel_records)
        statistics = self._generate_statistics(pdf_records, excel_records, issues)
        recommendations = self._generate_recommendations(issues)
        
        report = ValidationReport(
            total_pdf_records=len(pdf_records),
            total_excel_records=len(excel_records),
            match_rate=match_rate,
            issues=issues,
            statistics=statistics,
            recommendations=recommendations
        )
        
        logger.info(f"✅ Validation completed: {len(issues)} issues found, {match_rate:.1f}% match rate")
        return report
    
    def _extract_location(self, line: str) -> Optional[str]:
        """Extract location from a line if it's a location header"""
        line_upper = line.upper()
        
        # Check against known location patterns
        for pattern in self.location_patterns:
            match = re.search(pattern, line_upper)
            if match:
                location = match.group(1).strip()
                # Validate this is actually a location (not a property with location in name)
                if self._is_pure_location(line):
                    return location
        
        return None
    
    def _is_pure_location(self, line: str) -> bool:
        """Check if a line is a pure location header (not a property record)"""
        # If line contains property indicators, it's not a pure location
        property_indicators = [
            r'\b\d+Y\b',           # Size in yards
            r'\b[A-Z]-?\d+\b',     # Property codes
            r'\b\d{10}\b',         # Phone numbers
            r'\bGF\+FF\b',         # Floor structure
            r'\bBHK\b',            # Bedroom info
        ]
        
        for indicator in property_indicators:
            if re.search(indicator, line.upper()):
                return False
        
        return True
    
    def _extract_property_record(self, line: str, current_location: str, line_number: int) -> Optional[Dict[str, Any]]:
        """Extract property record information from a line"""
        record = {
            'line_number': line_number,
            'original_line': line,
            'location': current_location,
        }
        
        # Extract property code
        property_code = self._extract_property_code(line)
        if property_code:
            record['property_code'] = property_code
        
        # Extract size
        size = self._extract_size(line)
        if size:
            record['size'] = size
        
        # Extract property type
        property_type = self._extract_property_type(line)
        if property_type:
            record['property_type'] = property_type
        
        # Extract contact information
        contact_name, phone = self._extract_contact_info(line)
        if contact_name:
            record['contact_name'] = contact_name
        if phone:
            record['phone'] = phone
        
        # Only return if we found significant property information
        if any(key in record for key in ['property_code', 'size', 'contact_name']):
            return record
        
        return None
    
    def _extract_property_code(self, line: str) -> Optional[str]:
        """Extract property code from line"""
        for pattern in self.property_code_patterns:
            match = re.search(pattern, line)
            if match:
                return match.group(1)
        return None
    
    def _extract_size(self, line: str) -> Optional[str]:
        """Extract size information from line"""
        for pattern in self.size_patterns:
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                return f"{match.group(1)}Y"  # Normalize to yards
        return None
    
    def _extract_property_type(self, line: str) -> Optional[str]:
        """Extract property type from line"""
        line_upper = line.upper()
        for prop_type, pattern in self.property_type_indicators.items():
            if re.search(pattern, line_upper):
                return prop_type.title()
        return None
    
    def _extract_contact_info(self, line: str) -> Tuple[Optional[str], Optional[str]]:
        """Extract contact name and phone from line"""
        for pattern in self.contact_patterns:
            match = re.search(pattern, line)
            if match:
                name = match.group(1).strip()
                phone = match.group(2).strip()
                return name, phone
        return None, None
    
    def _find_missing_records(self, pdf_records: List[Dict], excel_records: List[Dict]) -> List[ValidationIssue]:
        """Find records that exist in PDF but are missing from Excel"""
        issues = []
        
        # Create sets of identifiers for comparison
        pdf_identifiers = set()
        excel_identifiers = set()
        
        for record in pdf_records:
            identifier = self._create_record_identifier(record)
            pdf_identifiers.add(identifier)
        
        for record in excel_records:
            identifier = self._create_record_identifier(record)
            excel_identifiers.add(identifier)
        
        # Find missing records
        missing_identifiers = pdf_identifiers - excel_identifiers
        
        for missing_id in missing_identifiers:
            # Find the original PDF record
            pdf_record = next((r for r in pdf_records if self._create_record_identifier(r) == missing_id), None)
            
            if pdf_record:
                issues.append(ValidationIssue(
                    issue_type="missing_record",
                    severity="critical",
                    description=f"Record missing from Excel: {pdf_record.get('property_code', 'Unknown')} in {pdf_record.get('location', 'Unknown')}",
                    line_number=pdf_record.get('line_number'),
                    expected_value=missing_id,
                    suggested_fix={
                        'action': 'add_record',
                        'record_data': pdf_record
                    }
                ))
        
        logger.info(f"🔍 Found {len(issues)} missing records")
        return issues
    
    def _find_location_mismatches(self, pdf_records: List[Dict], excel_records: List[Dict]) -> List[ValidationIssue]:
        """Find records with incorrect location assignments"""
        issues = []
        
        # Create location mapping from PDF
        pdf_location_map = {}
        for record in pdf_records:
            key = record.get('property_code', f"line_{record.get('line_number')}")
            pdf_location_map[key] = record.get('location')
        
        # Check Excel records against PDF locations
        for excel_record in excel_records:
            excel_key = excel_record.get('Property Code', f"row_{excel_record.get('_row_number', 0)}")
            excel_location = excel_record.get('Location/Area')
            expected_location = pdf_location_map.get(excel_key)
            
            if expected_location and excel_location != expected_location:
                issues.append(ValidationIssue(
                    issue_type="location_mismatch",
                    severity="high",
                    description=f"Location mismatch for {excel_key}",
                    expected_value=expected_location,
                    actual_value=excel_location,
                    suggested_fix={
                        'action': 'update_location',
                        'property_code': excel_key,
                        'correct_location': expected_location
                    }
                ))
        
        logger.info(f"🔍 Found {len(issues)} location mismatches")
        return issues
    
    def _find_property_code_errors(self, pdf_records: List[Dict], excel_records: List[Dict]) -> List[ValidationIssue]:
        """Find property code extraction errors"""
        issues = []
        
        # This is more complex and would benefit from AI validation
        # For now, check for obvious formatting issues
        for record in excel_records:
            prop_code = record.get('Property Code', '')
            if prop_code and not re.match(r'^[A-Z]{1,3}[-/]?\d+[A-Z]*$', prop_code):
                issues.append(ValidationIssue(
                    issue_type="property_code_format",
                    severity="medium",
                    description=f"Invalid property code format: {prop_code}",
                    actual_value=prop_code,
                    suggested_fix={
                        'action': 'review_property_code',
                        'property_code': prop_code
                    }
                ))
        
        logger.info(f"🔍 Found {len(issues)} property code issues")
        return issues
    
    def _find_size_discrepancies(self, pdf_records: List[Dict], excel_records: List[Dict]) -> List[ValidationIssue]:
        """Find size extraction discrepancies"""
        issues = []
        
        # Create size mapping from PDF
        pdf_size_map = {}
        for record in pdf_records:
            key = record.get('property_code', f"line_{record.get('line_number')}")
            pdf_size_map[key] = record.get('size')
        
        # Check Excel sizes against PDF
        for excel_record in excel_records:
            excel_key = excel_record.get('Property Code', f"row_{excel_record.get('_row_number', 0)}")
            excel_size = excel_record.get('Size/Yards')
            expected_size = pdf_size_map.get(excel_key)
            
            if expected_size and excel_size != expected_size:
                issues.append(ValidationIssue(
                    issue_type="size_mismatch",
                    severity="medium",
                    description=f"Size mismatch for {excel_key}",
                    expected_value=expected_size,
                    actual_value=excel_size,
                    suggested_fix={
                        'action': 'update_size',
                        'property_code': excel_key,
                        'correct_size': expected_size
                    }
                ))
        
        logger.info(f"🔍 Found {len(issues)} size discrepancies")
        return issues
    
    def _find_contact_issues(self, pdf_records: List[Dict], excel_records: List[Dict]) -> List[ValidationIssue]:
        """Find contact information extraction issues"""
        issues = []
        
        # Check for missing contact information
        for record in excel_records:
            contact_person = record.get('Contact Person', '')
            phone_numbers = record.get('Phone Numbers', '')
            
            if not contact_person and not phone_numbers:
                prop_code = record.get('Property Code', 'Unknown')
                issues.append(ValidationIssue(
                    issue_type="missing_contact",
                    severity="low",
                    description=f"Missing contact information for {prop_code}",
                    suggested_fix={
                        'action': 'extract_contact',
                        'property_code': prop_code
                    }
                ))
        
        logger.info(f"🔍 Found {len(issues)} contact issues")
        return issues
    
    def _create_record_identifier(self, record: Dict) -> str:
        """Create a unique identifier for a record for comparison"""
        # Use property code if available, otherwise use location + size combination
        if isinstance(record, dict):
            prop_code = record.get('Property Code') or record.get('property_code')
            if prop_code:
                return f"code:{prop_code}"
            
            location = record.get('Location/Area') or record.get('location', '')
            size = record.get('Size/Yards') or record.get('size', '')
            line_num = record.get('line_number', '')
            
            return f"loc:{location}:size:{size}:line:{line_num}"
        
        return str(hash(str(record)))
    
    def _calculate_match_rate(self, pdf_records: List[Dict], excel_records: List[Dict]) -> float:
        """Calculate the match rate between PDF and Excel records"""
        if not pdf_records:
            return 0.0
        
        pdf_identifiers = {self._create_record_identifier(r) for r in pdf_records}
        excel_identifiers = {self._create_record_identifier(r) for r in excel_records}
        
        matches = len(pdf_identifiers & excel_identifiers)
        match_rate = (matches / len(pdf_identifiers)) * 100
        
        return match_rate
    
    def _generate_statistics(self, pdf_records: List[Dict], excel_records: List[Dict], issues: List[ValidationIssue]) -> Dict[str, Any]:
        """Generate comprehensive statistics"""
        issue_types = defaultdict(int)
        severity_counts = defaultdict(int)
        
        for issue in issues:
            issue_types[issue.issue_type] += 1
            severity_counts[issue.severity] += 1
        
        return {
            'pdf_records': len(pdf_records),
            'excel_records': len(excel_records),
            'total_issues': len(issues),
            'issue_types': dict(issue_types),
            'severity_counts': dict(severity_counts),
            'location_distribution': self._get_location_distribution(pdf_records, excel_records)
        }
    
    def _get_location_distribution(self, pdf_records: List[Dict], excel_records: List[Dict]) -> Dict[str, Dict[str, int]]:
        """Get distribution of records by location"""
        pdf_locations = defaultdict(int)
        excel_locations = defaultdict(int)
        
        for record in pdf_records:
            location = record.get('location', 'Unknown')
            pdf_locations[location] += 1
        
        for record in excel_records:
            location = record.get('Location/Area', 'Unknown')
            excel_locations[location] += 1
        
        return {
            'pdf_distribution': dict(pdf_locations),
            'excel_distribution': dict(excel_locations)
        }
    
    def _generate_recommendations(self, issues: List[ValidationIssue]) -> List[str]:
        """Generate actionable recommendations based on issues found"""
        recommendations = []
        
        critical_issues = [i for i in issues if i.severity == 'critical']
        high_issues = [i for i in issues if i.severity == 'high']
        
        if critical_issues:
            recommendations.append(f"🚨 Address {len(critical_issues)} critical issues immediately (missing records)")
        
        if high_issues:
            recommendations.append(f"⚠️ Fix {len(high_issues)} high-priority issues (location mismatches)")
        
        # Type-specific recommendations
        missing_records = [i for i in issues if i.issue_type == 'missing_record']
        if missing_records:
            recommendations.append(f"📝 {len(missing_records)} records need to be added to Excel output")
        
        location_mismatches = [i for i in issues if i.issue_type == 'location_mismatch']
        if location_mismatches:
            recommendations.append(f"📍 {len(location_mismatches)} records have incorrect location assignments")
        
        if len(issues) > 20:
            recommendations.append("🤖 Consider using AI validation for complex pattern recognition")
        
        return recommendations
    
    def generate_detailed_report(self, report: ValidationReport) -> str:
        """Generate a detailed text report"""
        report_lines = [
            "=" * 80,
            "PROPERTY DATA VALIDATION REPORT",
            "=" * 80,
            f"PDF Records: {report.total_pdf_records}",
            f"Excel Records: {report.total_excel_records}",
            f"Match Rate: {report.match_rate:.1f}%",
            f"Total Issues: {len(report.issues)}",
            "",
            "ISSUE BREAKDOWN:",
        ]
        
        # Add issue type breakdown
        for issue_type, count in report.statistics['issue_types'].items():
            report_lines.append(f"  {issue_type}: {count}")
        
        report_lines.extend([
            "",
            "SEVERITY BREAKDOWN:",
        ])
        
        # Add severity breakdown
        for severity, count in report.statistics['severity_counts'].items():
            report_lines.append(f"  {severity}: {count}")
        
        report_lines.extend([
            "",
            "RECOMMENDATIONS:",
        ])
        
        # Add recommendations
        for recommendation in report.recommendations:
            report_lines.append(f"  • {recommendation}")
        
        # Add detailed issues (first 10)
        if report.issues:
            report_lines.extend([
                "",
                "DETAILED ISSUES (first 10):",
                "-" * 40,
            ])
            
            for i, issue in enumerate(report.issues[:10]):
                report_lines.extend([
                    f"{i+1}. {issue.issue_type.upper()}: {issue.description}",
                    f"   Severity: {issue.severity}",
                    f"   Line: {issue.line_number}" if issue.line_number else "",
                    f"   Expected: {issue.expected_value}" if issue.expected_value else "",
                    f"   Actual: {issue.actual_value}" if issue.actual_value else "",
                    ""
                ])
        
        return "\n".join(report_lines)

if __name__ == "__main__":
    # Test the pattern validator
    logging.basicConfig(level=logging.INFO)
    
    validator = PatternValidator()
    
    # Test with sample data
    sample_pdf_text = """
EAST OF KAILASH
G-50 200Y OLD HOUSE (RAKESH GUPTA VISTA 9810014028)
S-377 300Y BMT+GF+FF+SF+TF 12BHK (NARESH 9811070552)

MAHARANI BAGH
A-287 500Y BMT+GF+FF+SF+TF 3BHK MNRD (GREWAL 9569669999)
"""
    
    pdf_records = validator.extract_pdf_records(sample_pdf_text)
    print(f"Extracted {len(pdf_records)} records from sample PDF")
    
    for record in pdf_records:
        print(f"  {record}")