"""
Smart Validation Pipeline
Orchestrates pattern-based and AI validation for maximum accuracy at minimal cost
"""

import json
import logging
import time
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import pandas as pd

from pattern_validator import PatternValidator, ValidationReport
from ai_validator import AIValidator
from ollama_client import OllamaClient

logger = logging.getLogger(__name__)

class SmartValidationPipeline:
    """
    Intelligent validation pipeline that combines multiple validation approaches
    for maximum accuracy while minimizing AI usage costs
    """
    
    def __init__(self, model: str = "llama3.1:8b", auto_correct: bool = False):
        self.pattern_validator = PatternValidator()
        self.ai_validator = AIValidator(model)
        self.auto_correct = auto_correct
        
        # Pipeline configuration
        self.config = {
            "pattern_validation": True,      # Always run pattern validation
            "ai_validation": True,           # Use AI for complex issues
            "auto_correction": auto_correct, # Apply corrections automatically
            "confidence_threshold": 0.8,     # Minimum confidence for auto-correction
            "max_ai_issues": 20,            # Max issues to send to AI (cost control)
        }
        
        # Statistics tracking
        self.stats = {
            "validations_performed": 0,
            "pattern_issues_found": 0,
            "ai_issues_resolved": 0,
            "corrections_applied": 0,
            "processing_time": 0.0,
        }
        
        logger.info(f"🚀 Smart Validation Pipeline initialized (Model: {model}, Auto-correct: {auto_correct})")
    
    def validate_pdf_to_excel(self, pdf_path: str, excel_path: str, 
                             output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Complete validation workflow from PDF to Excel with optional correction
        
        Args:
            pdf_path: Path to source PDF file
            excel_path: Path to generated Excel file
            output_path: Optional path for corrected Excel file
            
        Returns:
            Comprehensive validation results
        """
        start_time = time.time()
        logger.info(f"🔍 Starting validation: {Path(pdf_path).name} → {Path(excel_path).name}")
        
        try:
            # Step 1: Load data
            pdf_text = self._load_pdf_text(pdf_path)
            excel_records = self._load_excel_records(excel_path)
            
            if not pdf_text:
                raise ValueError(f"Could not extract text from PDF: {pdf_path}")
            
            if not excel_records:
                raise ValueError(f"Could not load Excel records from: {excel_path}")
            
            logger.info(f"📊 Loaded: PDF text ({len(pdf_text)} chars), Excel records ({len(excel_records)})")
            
            # Step 2: Run comprehensive validation
            validation_report = self.ai_validator.validate_comprehensive(pdf_text, excel_records)
            
            # Step 3: Apply corrections if enabled
            corrected_records = excel_records
            corrections_applied = 0
            
            if self.auto_correct and validation_report.issues:
                logger.info("🔧 Auto-correction enabled, applying fixes...")
                corrected_records, corrections_applied = self.ai_validator.apply_corrections(
                    excel_records, validation_report.issues
                )
                
                # Save corrected Excel if output path provided
                if output_path and corrections_applied > 0:
                    self._save_corrected_excel(corrected_records, output_path)
                    logger.info(f"💾 Saved corrected Excel: {output_path}")
            
            # Step 4: Generate comprehensive results
            processing_time = time.time() - start_time
            
            results = {
                "validation_report": validation_report,
                "corrections_applied": corrections_applied,
                "corrected_records": corrected_records if corrections_applied > 0 else None,
                "processing_time": processing_time,
                "statistics": self._generate_validation_statistics(validation_report, corrections_applied),
                "recommendations": self._generate_actionable_recommendations(validation_report),
                "files": {
                    "source_pdf": pdf_path,
                    "original_excel": excel_path,
                    "corrected_excel": output_path if corrections_applied > 0 else None
                }
            }
            
            # Update internal statistics
            self.stats["validations_performed"] += 1
            self.stats["pattern_issues_found"] += len(validation_report.issues)
            self.stats["corrections_applied"] += corrections_applied
            self.stats["processing_time"] += processing_time
            
            logger.info(f"✅ Validation completed in {processing_time:.2f}s: {validation_report.match_rate:.1f}% accuracy")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {str(e)}")
            return {
                "error": str(e),
                "processing_time": time.time() - start_time,
                "validation_report": None
            }
    
    def validate_records_direct(self, pdf_text: str, excel_records: List[Dict]) -> ValidationReport:
        """
        Direct validation of records without file I/O
        
        Args:
            pdf_text: PDF text content
            excel_records: Excel records list
            
        Returns:
            Validation report
        """
        logger.info(f"🔍 Direct validation: {len(pdf_text)} chars PDF, {len(excel_records)} Excel records")
        
        return self.ai_validator.validate_comprehensive(pdf_text, excel_records)
    
    def batch_validate(self, file_pairs: List[Tuple[str, str]], 
                      output_dir: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Batch validation of multiple PDF-Excel pairs
        
        Args:
            file_pairs: List of (pdf_path, excel_path) tuples
            output_dir: Optional directory for corrected Excel files
            
        Returns:
            List of validation results
        """
        logger.info(f"🔄 Starting batch validation of {len(file_pairs)} file pairs...")
        
        results = []
        for i, (pdf_path, excel_path) in enumerate(file_pairs, 1):
            logger.info(f"📄 Processing pair {i}/{len(file_pairs)}: {Path(pdf_path).name}")
            
            # Prepare output path if directory provided
            output_path = None
            if output_dir:
                output_dir_path = Path(output_dir)
                output_dir_path.mkdir(exist_ok=True)
                output_path = str(output_dir_path / f"corrected_{Path(excel_path).name}")
            
            # Validate
            result = self.validate_pdf_to_excel(pdf_path, excel_path, output_path)
            result["batch_index"] = i
            results.append(result)
            
            # Small delay to prevent overwhelming the system
            time.sleep(0.1)
        
        # Generate batch summary
        batch_summary = self._generate_batch_summary(results)
        logger.info(f"✅ Batch validation completed: {batch_summary}")
        
        return results
    
    def _load_pdf_text(self, pdf_path: str) -> str:
        """Load text content from PDF file"""
        try:
            # Use existing PDF parser
            from pdf_parser import PDFParser
            parser = PDFParser()
            extracted_data = parser.extract_from_multiple_pdfs([pdf_path])
            
            # Get the text for this specific file
            if extracted_data and pdf_path in extracted_data:
                text = extracted_data[pdf_path]
                logger.info(f"📝 Loaded PDF text: {len(text)} characters")
                return text
            else:
                logger.error(f"❌ No text extracted from PDF: {pdf_path}")
                return ""
                
        except Exception as e:
            logger.error(f"❌ Failed to load PDF text: {str(e)}")
            import traceback
            logger.error(f"🔍 Full traceback: {traceback.format_exc()}")
            return ""
    
    def _load_excel_records(self, excel_path: str) -> List[Dict[str, Any]]:
        """Load records from Excel file"""
        try:
            # Check what sheets are available
            excel_file = pd.ExcelFile(excel_path)
            available_sheets = excel_file.sheet_names
            logger.info(f"📊 Available sheets: {available_sheets}")
            
            # Try to find the main data sheet (prioritize by likely names)
            main_sheet_candidates = ['All Property Data', 'Main Data', 'Data', 'Properties']
            main_sheet = None
            
            for candidate in main_sheet_candidates:
                if candidate in available_sheets:
                    main_sheet = candidate
                    break
            
            # If no main sheet found, use the largest sheet
            if not main_sheet:
                sheet_sizes = {}
                for sheet_name in available_sheets:
                    try:
                        df_test = pd.read_excel(excel_path, sheet_name=sheet_name)
                        sheet_sizes[sheet_name] = len(df_test)
                    except:
                        sheet_sizes[sheet_name] = 0
                
                # Find sheet with most records (excluding summary-type sheets)
                non_summary_sheets = {k: v for k, v in sheet_sizes.items() 
                                    if 'summary' not in k.lower() and v > 0}
                
                if non_summary_sheets:
                    main_sheet = max(non_summary_sheets, key=non_summary_sheets.get)
                    logger.info(f"📊 Selected largest non-summary sheet: {main_sheet} ({non_summary_sheets[main_sheet]} records)")
                else:
                    main_sheet = available_sheets[0]  # Fallback to first sheet
            
            logger.info(f"📊 Reading main data from sheet: {main_sheet}")
            
            # Read the main data sheet
            df = pd.read_excel(excel_path, sheet_name=main_sheet)
            
            # Convert to list of dictionaries
            records = df.to_dict('records')
            
            # Add row numbers for tracking
            for i, record in enumerate(records):
                record['_row_number'] = i + 2  # Excel row (accounting for header)
                record['_source_sheet'] = main_sheet
            
            logger.info(f"📊 Loaded {len(records)} records from {excel_path} (sheet: {main_sheet})")
            return records
            
        except Exception as e:
            logger.error(f"❌ Failed to load Excel records: {str(e)}")
            import traceback
            logger.error(f"🔍 Full traceback: {traceback.format_exc()}")
            return []
    
    def _save_corrected_excel(self, records: List[Dict], output_path: str):
        """Save corrected records to Excel file"""
        try:
            # Remove internal tracking fields
            clean_records = []
            for record in records:
                clean_record = {k: v for k, v in record.items() if not k.startswith('_')}
                clean_records.append(clean_record)
            
            # Create DataFrame and save
            df = pd.DataFrame(clean_records)
            df.to_excel(output_path, index=False)
            
            logger.info(f"💾 Saved {len(clean_records)} corrected records to {output_path}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save corrected Excel: {str(e)}")
    
    def _generate_validation_statistics(self, report: ValidationReport, corrections_applied: int) -> Dict[str, Any]:
        """Generate comprehensive validation statistics"""
        return {
            "accuracy": {
                "match_rate": report.match_rate,
                "total_pdf_records": report.total_pdf_records,
                "total_excel_records": report.total_excel_records,
                "issues_found": len(report.issues),
                "corrections_applied": corrections_applied
            },
            "issue_breakdown": {
                "critical": len([i for i in report.issues if i.severity == 'critical']),
                "high": len([i for i in report.issues if i.severity == 'high']),
                "medium": len([i for i in report.issues if i.severity == 'medium']),
                "low": len([i for i in report.issues if i.severity == 'low'])
            },
            "validation_methods": {
                "pattern_based": len([i for i in report.issues if i.confidence >= 0.9]),
                "ai_enhanced": len([i for i in report.issues if 0.7 <= i.confidence < 0.9]),
                "needs_review": len([i for i in report.issues if i.confidence < 0.7])
            },
            "improvement_potential": {
                "auto_correctable": len([i for i in report.issues if i.suggested_fix and i.confidence >= 0.8]),
                "manual_review_needed": len([i for i in report.issues if not i.suggested_fix or i.confidence < 0.8])
            }
        }
    
    def _generate_actionable_recommendations(self, report: ValidationReport) -> List[Dict[str, Any]]:
        """Generate specific, actionable recommendations"""
        recommendations = []
        
        # Critical issues
        critical_issues = [i for i in report.issues if i.severity == 'critical']
        if critical_issues:
            recommendations.append({
                "priority": "critical",
                "action": "immediate_attention",
                "description": f"Address {len(critical_issues)} critical issues (missing records)",
                "estimated_impact": f"+{len(critical_issues) * 1.4:.0f}% accuracy improvement",
                "time_required": "15-30 minutes"
            })
        
        # Auto-correctable issues
        auto_correctable = [i for i in report.issues if i.suggested_fix and i.confidence >= 0.8]
        if auto_correctable:
            recommendations.append({
                "priority": "high",
                "action": "enable_auto_correction",
                "description": f"Enable auto-correction for {len(auto_correctable)} high-confidence fixes",
                "estimated_impact": f"+{len(auto_correctable) * 1.0:.0f}% accuracy improvement",
                "time_required": "Automatic"
            })
        
        # Location mismatches
        location_issues = [i for i in report.issues if i.issue_type == 'location_mismatch']
        if location_issues:
            recommendations.append({
                "priority": "high",
                "action": "fix_location_context",
                "description": f"Fix location context tracking ({len(location_issues)} mismatches)",
                "estimated_impact": f"+{len(location_issues) * 0.8:.0f}% accuracy improvement",
                "time_required": "30-60 minutes"
            })
        
        # Pattern improvements
        if report.match_rate < 80:
            recommendations.append({
                "priority": "medium",
                "action": "enhance_patterns",
                "description": "Enhance pattern recognition for better extraction",
                "estimated_impact": "+10-15% accuracy improvement",
                "time_required": "2-4 hours"
            })
        
        return recommendations
    
    def _generate_batch_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics for batch validation"""
        if not results:
            return {}
        
        valid_results = [r for r in results if r.get('validation_report')]
        
        if not valid_results:
            return {"error": "No valid validation results"}
        
        # Calculate aggregate statistics
        total_pdf_records = sum(r['validation_report'].total_pdf_records for r in valid_results)
        total_excel_records = sum(r['validation_report'].total_excel_records for r in valid_results)
        total_issues = sum(len(r['validation_report'].issues) for r in valid_results)
        total_corrections = sum(r.get('corrections_applied', 0) for r in valid_results)
        
        avg_match_rate = sum(r['validation_report'].match_rate for r in valid_results) / len(valid_results)
        total_processing_time = sum(r.get('processing_time', 0) for r in valid_results)
        
        return {
            "files_processed": len(results),
            "successful_validations": len(valid_results),
            "aggregate_stats": {
                "total_pdf_records": total_pdf_records,
                "total_excel_records": total_excel_records,
                "average_match_rate": avg_match_rate,
                "total_issues_found": total_issues,
                "total_corrections_applied": total_corrections
            },
            "performance": {
                "total_processing_time": total_processing_time,
                "average_time_per_file": total_processing_time / len(valid_results) if valid_results else 0,
                "files_per_minute": len(valid_results) / (total_processing_time / 60) if total_processing_time > 0 else 0
            }
        }
    
    def generate_detailed_report(self, validation_results: Dict[str, Any]) -> str:
        """Generate a comprehensive validation report"""
        if validation_results.get('error'):
            return f"VALIDATION FAILED: {validation_results['error']}"
        
        report = validation_results.get('validation_report')
        if not report:
            return "No validation report available"
        
        stats = validation_results.get('statistics', {})
        recommendations = validation_results.get('recommendations', [])
        
        lines = [
            "=" * 80,
            "SMART VALIDATION REPORT",
            "=" * 80,
            f"Processing Time: {validation_results.get('processing_time', 0):.2f}s",
            f"Match Rate: {report.match_rate:.1f}%",
            f"PDF Records: {report.total_pdf_records}",
            f"Excel Records: {report.total_excel_records}",
            f"Issues Found: {len(report.issues)}",
            f"Corrections Applied: {validation_results.get('corrections_applied', 0)}",
            "",
            "ACCURACY BREAKDOWN:",
            f"  Critical Issues: {stats.get('issue_breakdown', {}).get('critical', 0)}",
            f"  High Priority: {stats.get('issue_breakdown', {}).get('high', 0)}",
            f"  Medium Priority: {stats.get('issue_breakdown', {}).get('medium', 0)}",
            f"  Low Priority: {stats.get('issue_breakdown', {}).get('low', 0)}",
            "",
            "VALIDATION METHODS:",
            f"  Pattern-Based: {stats.get('validation_methods', {}).get('pattern_based', 0)}",
            f"  AI-Enhanced: {stats.get('validation_methods', {}).get('ai_enhanced', 0)}",
            f"  Needs Review: {stats.get('validation_methods', {}).get('needs_review', 0)}",
            "",
            "ACTIONABLE RECOMMENDATIONS:",
        ]
        
        for i, rec in enumerate(recommendations, 1):
            lines.extend([
                f"{i}. {rec.get('description', 'No description')}",
                f"   Priority: {rec.get('priority', 'Unknown').upper()}",
                f"   Impact: {rec.get('estimated_impact', 'Unknown')}",
                f"   Time: {rec.get('time_required', 'Unknown')}",
                ""
            ])
        
        # Add file information
        files = validation_results.get('files', {})
        if files:
            lines.extend([
                "FILES:",
                f"  Source PDF: {files.get('source_pdf', 'N/A')}",
                f"  Original Excel: {files.get('original_excel', 'N/A')}",
                f"  Corrected Excel: {files.get('corrected_excel', 'Not created')}",
            ])
        
        return "\n".join(lines)
    
    def get_pipeline_statistics(self) -> Dict[str, Any]:
        """Get overall pipeline usage statistics"""
        return {
            "pipeline_stats": self.stats.copy(),
            "ai_client_available": self.ai_validator.ollama_client.is_available(),
            "configuration": self.config.copy()
        }

# Factory function for easy initialization
def create_smart_validator(model: str = "llama3.1:8b", auto_correct: bool = False) -> SmartValidationPipeline:
    """
    Factory function to create a configured smart validator
    
    Args:
        model: Ollama model to use
        auto_correct: Enable automatic correction
        
    Returns:
        Configured SmartValidationPipeline
    """
    return SmartValidationPipeline(model=model, auto_correct=auto_correct)

if __name__ == "__main__":
    # Test the smart validation pipeline
    logging.basicConfig(level=logging.INFO)
    
    # Create validator
    validator = create_smart_validator(auto_correct=True)
    
    # Test with sample files (if they exist)
    pdf_path = r"C:\Users\<USER>\Downloads\pdf to excel\pdfs\KOTHI SALE JULY-2025.pdf"
    excel_path = r"C:\Users\<USER>\Downloads\pdf to excel\Real_Estate_Data_20250731.xlsx"
    
    if Path(pdf_path).exists() and Path(excel_path).exists():
        print("🧪 Testing validation pipeline...")
        
        results = validator.validate_pdf_to_excel(
            pdf_path=pdf_path,
            excel_path=excel_path,
            output_path=r"C:\Users\<USER>\Downloads\pdf to excel\corrected_output.xlsx"
        )
        
        print("\n" + validator.generate_detailed_report(results))
        print(f"\nPipeline Statistics: {validator.get_pipeline_statistics()}")
    else:
        print("📁 Test files not found, skipping validation test")
        print("✅ Smart Validation Pipeline initialized successfully")