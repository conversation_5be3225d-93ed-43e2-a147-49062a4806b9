#!/usr/bin/env python3
"""
Comprehensive PDF to Excel comparison tool
Performs 1-to-1 analysis to identify missing or incorrect records
"""

import pandas as pd
import re
from pdf_parser import PDFParser
from typing import List, Dict, Set, Tuple

class PDFExcelComparator:
    def __init__(self):
        self.pdf_records = []
        self.excel_records = []
        self.missing_records = []
        self.incorrect_records = []
        self.extra_records = []
    
    def extract_pdf_records(self, pdf_path: str) -> List[Dict]:
        """Extract all property records manually from PDF text"""
        parser = PDFParser()
        text = parser.extract_text_from_pdf(pdf_path)
        
        if not text:
            print("Failed to extract text from PDF")
            return []
        
        lines = text.strip().split('\n')
        records = []
        current_location = ""
        
        print(f"Analyzing {len(lines)} lines from PDF...")
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or len(line) < 10:
                continue
                
            # Skip document headers
            if line in ['F', 'SURVEY REPORT JULY-2025']:
                continue
            
            # Detect location headers
            if self.is_location_header(line):
                # Handle mixed location-property lines
                if self.is_mixed_location_property(line):
                    location, property_part = self.split_location_property(line)
                    current_location = location
                    if property_part:
                        record = self.parse_property_record(property_part, current_location, line_num)
                        if record:
                            records.append(record)
                else:
                    current_location = self.clean_location_name(line)
                continue
            
            # Parse property records
            if current_location and self.looks_like_property_record(line):
                record = self.parse_property_record(line, current_location, line_num)
                if record:
                    records.append(record)
        
        print(f"Extracted {len(records)} records from PDF")
        return records
    
    def is_location_header(self, line: str) -> bool:
        """Check if line is a location header"""
        line_upper = line.upper()
        
        # Location keywords
        location_keywords = ['COLONY', 'NAGAR', 'PARK', 'VIHAR', 'ENCLAVE', 'MARKET', 'MKT', 
                           'BLOCK', 'BLK', 'EXTENSION', 'EXTN', 'BAGH', 'PLACE', 'LANE', 'KHAS',
                           'WEST', 'EAST', 'NORTH', 'SOUTH', 'CENTRAL', 'KAILASH', 'FRIENDS']
        
        # Check for location patterns
        has_location_keyword = any(keyword in line_upper for keyword in location_keywords)
        
        # Specific patterns
        specific_patterns = [
            r'^[A-Z][\w\s\.\-–]*(?:COLONY|NAGAR|PARK|VIHAR|ENCLAVE|MARKET|BLOCK)[-\d\s]*$',
            r'^DEFENCE\s+COLONY$',
            r'^G\.K[-\d]*$',
            r'^LAJPAT\s+NAGAR[-\d]*$',
            r'^NEW\s+FRIENDS\s+COLONY',
            r'^FRIENDS\s+COLONY\s+(EAST|WEST)',
            r'^EAST\s+OF\s+KAILASH$',
            r'^SOUTH\s+EX[-\d]*$',
            r'^C\.\s*R\.\s*PARK',
        ]
        
        pattern_match = any(re.match(pattern, line_upper) for pattern in specific_patterns)
        
        # Check if it ends with dash (mixed location-property)
        ends_with_dash = re.search(r'[-–]\s*$', line)
        
        # Exclude obvious property lines
        property_indicators = ['Y ', 'BHK', '(', ')', 'GF+', 'FF+', 'SF+', 'TF+', 'BMT+']
        has_property_indicators = any(indicator in line_upper for indicator in property_indicators)
        
        return (has_location_keyword or pattern_match or ends_with_dash) and not has_property_indicators
    
    def is_mixed_location_property(self, line: str) -> bool:
        """Check if line contains both location and property data"""
        # Look for patterns like "LOCATION- A-123 500Y ..." or "LOCATION A-123 500Y ..."
        mixed_patterns = [
            r'^([A-Z][A-Z\s\.\-–]*(?:COLONY|NAGAR|PARK|VIHAR|ENCLAVE|MARKET|BLOCK|EXTENSION|EXTN|BAGH|PLACE|LANE|KHAS)[-\d]*)\s*[-–]\s*([A-Z]{1,3}[-/]?\d+[A-Z]*\s+.+)$',
            r'^(LAJPAT\s+NAGAR[-]?\d*)\s+([A-Z]{1,3}[-/]?\d+[A-Z]*\s+.+)$',
            r'^(C\.\s*R\.\s*PARK)\s*[-–]\s*(.+)$',
            r'^(KAROL\s+BAGH)\s*[-–]\s*(.+)$',
        ]
        
        return any(re.match(pattern, line, re.IGNORECASE) for pattern in mixed_patterns)
    
    def split_location_property(self, line: str) -> Tuple[str, str]:
        """Split mixed location-property line into location and property parts"""
        mixed_patterns = [
            r'^([A-Z][A-Z\s\.\-–]*(?:COLONY|NAGAR|PARK|VIHAR|ENCLAVE|MARKET|BLOCK|EXTENSION|EXTN|BAGH|PLACE|LANE|KHAS)[-\d]*)\s*[-–]\s*([A-Z]{1,3}[-/]?\d+[A-Z]*\s+.+)$',
            r'^(LAJPAT\s+NAGAR[-]?\d*)\s+([A-Z]{1,3}[-/]?\d+[A-Z]*\s+.+)$',
            r'^(C\.\s*R\.\s*PARK)\s*[-–]\s*(.+)$',
            r'^(KAROL\s+BAGH)\s*[-–]\s*(.+)$',
        ]
        
        for pattern in mixed_patterns:
            match = re.match(pattern, line, re.IGNORECASE)
            if match:
                location = match.group(1).strip().rstrip('-–').strip()
                property_part = match.group(2).strip()
                return location, property_part
        
        return line, ""
    
    def clean_location_name(self, location: str) -> str:
        """Clean location name"""
        return location.strip().rstrip('-–').strip()
    
    def looks_like_property_record(self, line: str) -> bool:
        """Check if line looks like a property record"""
        # Property indicators
        property_patterns = [
            r'[A-Z]{1,3}[-/]?\d+[A-Z]*\s+\d+Y',  # Property code + size
            r'\d+Y\s+[A-Z]',  # Size + type
            r'\([A-Z\s]+\s+\d{10}',  # Contact info
            r'(BMT|GF|FF|SF|TF)\+',  # Building structure
            r'\d+BHK',  # Bedroom info
        ]
        
        return any(re.search(pattern, line) for pattern in property_patterns)
    
    def parse_property_record(self, line: str, location: str, line_num: int) -> Dict:
        """Parse a property record from line"""
        record = {
            'line_number': line_num,
            'original_line': line,
            'location': location,
            'property_code': '',
            'size_yards': '',
            'property_type': '',
            'details': '',
            'contact_person': '',
            'phone_numbers': '',
            'status': ''
        }
        
        # Extract property code
        code_match = re.search(r'\b([A-Z]{1,3}[-/]?\d+[A-Z]*)\b', line)
        if code_match:
            record['property_code'] = code_match.group(1)
        
        # Extract size
        size_match = re.search(r'(\d+(?:\.\d+)?Y)', line)
        if size_match:
            record['size_yards'] = size_match.group(1)
        
        # Extract property type
        type_patterns = ['DUPLEX', 'BUNGALOW', 'KOTHI', 'HOUSE', 'PLOT', 'SHOP', 'FARM']
        for prop_type in type_patterns:
            if prop_type in line.upper():
                record['property_type'] = prop_type.title()
                break
        
        # Extract contact info
        contact_match = re.search(r'\(([^)]*?)\s+(\d{10,})', line)
        if contact_match:
            record['contact_person'] = contact_match.group(1).strip()
            record['phone_numbers'] = contact_match.group(2)
        
        # Extract status
        status_patterns = ['P/D', 'NOT CONFIRM', 'MANDATE', 'FREE HOLD', 'LEASE HOLD', 'OUTRIGHT']
        for status in status_patterns:
            if status in line.upper():
                record['status'] = status
                break
        
        return record
    
    def load_excel_records(self, excel_path: str) -> List[Dict]:
        """Load records from Excel file"""
        try:
            # Load the Kothi Sale sheet
            df = pd.read_excel(excel_path, sheet_name='Kothi Sale')
            records = []
            
            for idx, row in df.iterrows():
                record = {
                    'excel_row': idx + 2,  # +2 for header and 0-based index
                    'location': str(row.get('Location/Area', '')).strip(),
                    'property_code': str(row.get('Property Code', '')).strip(),
                    'size_yards': str(row.get('Size/Yards', '')).strip(),
                    'property_type': str(row.get('Property Type', '')).strip(),
                    'details': str(row.get('Details', '')).strip(),
                    'contact_person': str(row.get('Contact Person', '')).strip(),
                    'phone_numbers': str(row.get('Phone Numbers', '')).strip(),
                    'status': str(row.get('Status', '')).strip()
                }
                
                # Clean up nan values
                for key, value in record.items():
                    if value in ['nan', 'None', '']:
                        record[key] = ''
                
                records.append(record)
            
            print(f"Loaded {len(records)} records from Excel")
            return records
            
        except Exception as e:
            print(f"Error loading Excel file: {e}")
            return []
    
    def compare_records(self):
        """Compare PDF and Excel records"""
        print("\n" + "="*80)
        print("DETAILED COMPARISON ANALYSIS")
        print("="*80)
        
        # Create lookup sets for comparison
        pdf_signatures = set()
        excel_signatures = set()
        
        # Create signatures for PDF records
        for record in self.pdf_records:
            signature = f"{record['location']}|{record['property_code']}|{record['size_yards']}"
            pdf_signatures.add(signature)
        
        # Create signatures for Excel records  
        for record in self.excel_records:
            signature = f"{record['location']}|{record['property_code']}|{record['size_yards']}"
            excel_signatures.add(signature)
        
        # Find missing records (in PDF but not in Excel)
        missing_signatures = pdf_signatures - excel_signatures
        for signature in missing_signatures:
            location, code, size = signature.split('|')
            pdf_record = next((r for r in self.pdf_records 
                             if r['location'] == location and r['property_code'] == code and r['size_yards'] == size), None)
            if pdf_record:
                self.missing_records.append(pdf_record)
        
        # Find extra records (in Excel but not in PDF)
        extra_signatures = excel_signatures - pdf_signatures
        for signature in extra_signatures:
            location, code, size = signature.split('|')
            excel_record = next((r for r in self.excel_records 
                               if r['location'] == location and r['property_code'] == code and r['size_yards'] == size), None)
            if excel_record:
                self.extra_records.append(excel_record)
        
        self.print_comparison_results()
    
    def print_comparison_results(self):
        """Print detailed comparison results"""
        print(f"\nCOMPARISON STATISTICS:")
        print(f"   PDF Records Found: {len(self.pdf_records)}")
        print(f"   Excel Records Found: {len(self.excel_records)}")
        print(f"   Missing Records (in PDF but not Excel): {len(self.missing_records)}")
        print(f"   Extra Records (in Excel but not PDF): {len(self.extra_records)}")
        print(f"   Match Rate: {((len(self.pdf_records) - len(self.missing_records)) / len(self.pdf_records) * 100):.1f}%")
        
        if self.missing_records:
            print(f"\nMISSING RECORDS FROM EXCEL ({len(self.missing_records)}):")
            for i, record in enumerate(self.missing_records, 1):
                print(f"\n   #{i} - Line {record['line_number']}: {record['location']}")
                print(f"      Property: {record['property_code']} {record['size_yards']}")
                print(f"      Original: {record['original_line'][:100]}...")
        
        if self.extra_records:
            print(f"\nEXTRA RECORDS IN EXCEL ({len(self.extra_records)}):")
            for i, record in enumerate(self.extra_records, 1):
                print(f"\n   #{i} - Excel Row {record['excel_row']}: {record['location']}")
                print(f"      Property: {record['property_code']} {record['size_yards']}")
                print(f"      Details: {record['details'][:100]}...")
        
        # Location-wise breakdown
        print(f"\nLOCATION-WISE BREAKDOWN:")
        pdf_by_location = {}
        excel_by_location = {}
        
        for record in self.pdf_records:
            loc = record['location']
            pdf_by_location[loc] = pdf_by_location.get(loc, 0) + 1
        
        for record in self.excel_records:
            loc = record['location']
            excel_by_location[loc] = excel_by_location.get(loc, 0) + 1
        
        all_locations = set(pdf_by_location.keys()) | set(excel_by_location.keys())
        
        for location in sorted(all_locations):
            pdf_count = pdf_by_location.get(location, 0)
            excel_count = excel_by_location.get(location, 0)
            status = "OK" if pdf_count == excel_count else "DIFF"
            print(f"   {status} {location}: PDF={pdf_count}, Excel={excel_count}")

def main():
    pdf_path = r"C:\Users\<USER>\Downloads\pdf to excel\pdfs\KOTHI SALE JULY-2025.pdf"
    excel_path = r"C:\Users\<USER>\Downloads\pdf to excel\Real_Estate_Data_20250731.xlsx"
    
    comparator = PDFExcelComparator()
    
    print("Starting comprehensive PDF to Excel comparison...")
    
    # Extract records from PDF
    print("\n1. Extracting records from PDF...")
    comparator.pdf_records = comparator.extract_pdf_records(pdf_path)
    
    # Load records from Excel
    print("\n2. Loading records from Excel...")
    comparator.excel_records = comparator.load_excel_records(excel_path)
    
    # Compare records
    print("\n3. Comparing records...")
    comparator.compare_records()

if __name__ == "__main__":
    main()