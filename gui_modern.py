"""
Modern GUI Module - Enhanced PDF to Excel Converter Interface
Beautiful, functional, and user-friendly interface with advanced features
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import tkinter.font as tkFont
import threading
import os
import glob
import datetime
import json
from typing import List, Callable, Optional, Dict, Any
import logging
from config import GUI_CONFIG

# Configure logging
logger = logging.getLogger(__name__)

class ModernPDFConverterGUI:
    """
    Modern, beautiful GUI for PDF to Excel Converter with enhanced functionality
    """
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_styles()
        self.load_settings()
        self.create_widgets()
        self.selected_files = []
        self.conversion_callback: Optional[Callable] = None
        self.settings = self.load_user_settings()
        
    def setup_window(self):
        """Setup main window with modern styling"""
        self.root.title("PDF to Excel Converter - Professional Edition")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        self.root.configure(bg='#f8f9fa')
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_reqwidth() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_reqheight() // 2)
        self.root.geometry(f"+{x}+{y}")
        
        # Configure grid weights for responsive design
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def create_styles(self):
        """Create modern styling themes"""
        self.style = ttk.Style()
        
        # Configure modern color scheme
        self.colors = {
            'primary': '#2563eb',      # Blue
            'primary_hover': '#1d4ed8',
            'secondary': '#64748b',    # Gray
            'success': '#10b981',      # Green
            'warning': '#f59e0b',      # Orange
            'danger': '#ef4444',       # Red
            'light': '#f8fafc',        # Very light gray
            'dark': '#1e293b',         # Dark gray
            'white': '#ffffff',
            'border': '#e2e8f0'        # Light border
        }
        
        # Modern button styles
        self.style.configure('Primary.TButton',
                           background=self.colors['primary'],
                           foreground='white',
                           font=('Segoe UI', 11, 'bold'),
                           borderwidth=0,
                           relief='flat',
                           padding=(20, 12))
        
        self.style.map('Primary.TButton',
                      background=[('active', self.colors['primary_hover']),
                                ('pressed', self.colors['primary_hover'])])
        
        self.style.configure('Secondary.TButton',
                           background=self.colors['secondary'],
                           foreground='white',
                           font=('Segoe UI', 10),
                           borderwidth=0,
                           relief='flat',
                           padding=(15, 8))
        
        # Modern frame styles
        self.style.configure('Card.TFrame',
                           background='white',
                           relief='flat',
                           borderwidth=1)
        
        # Modern label styles
        self.style.configure('Title.TLabel',
                           background='white',
                           foreground=self.colors['dark'],
                           font=('Segoe UI', 24, 'bold'))
        
        self.style.configure('Subtitle.TLabel',
                           background='white',
                           foreground=self.colors['secondary'],
                           font=('Segoe UI', 12))
        
        self.style.configure('SectionHeader.TLabel',
                           background='white',
                           foreground=self.colors['dark'],
                           font=('Segoe UI', 14, 'bold'))
        
        # Modern entry styles
        self.style.configure('Modern.TEntry',
                           fieldbackground='white',
                           borderwidth=2,
                           relief='solid',
                           font=('Segoe UI', 10))
        
        # Progress bar style
        self.style.configure('Modern.Horizontal.TProgressbar',
                           background=self.colors['primary'],
                           borderwidth=0,
                           lightcolor=self.colors['primary'],
                           darkcolor=self.colors['primary'])
    
    def load_settings(self):
        """Load application settings"""
        self.app_settings = {
            'theme': 'light',
            'auto_open_excel': True,
            'remember_location': True,
            'default_output_format': 'xlsx',
            'include_summary': True,
            'create_category_sheets': True
        }
    
    def load_user_settings(self) -> Dict[str, Any]:
        """Load user settings from file"""
        settings_file = 'user_settings.json'
        default_settings = {
            'last_output_directory': os.getcwd(),
            'output_filename_pattern': 'Real_Estate_Data_{date}',
            'preferred_format': 'xlsx',
            'auto_open_result': True,
            'include_summary_sheet': True,
            'create_category_sheets': True,
            'window_geometry': '1200x800',
            'ai_validation_enabled': False,
            'ai_model': 'llama3.1:8b',
            'ai_auto_correct': True
        }
        
        try:
            if os.path.exists(settings_file):
                with open(settings_file, 'r') as f:
                    saved_settings = json.load(f)
                    default_settings.update(saved_settings)
        except Exception as e:
            logger.warning(f"Could not load settings: {e}")
        
        return default_settings
    
    def save_user_settings(self):
        """Save user settings to file"""
        try:
            # Update settings from UI
            self.settings['ai_validation_enabled'] = self.ai_validation_var.get() if hasattr(self, 'ai_validation_var') else False
            
            with open('user_settings.json', 'w') as f:
                json.dump(self.settings, f, indent=2)
        except Exception as e:
            logger.warning(f"Could not save settings: {e}")
    
    def _check_ai_availability(self):
        """Check if AI validation is available"""
        try:
            from ollama_client import create_ollama_client
            client = create_ollama_client(self.settings['ai_model'])
            test_result = client.test_connection()
            
            if test_result.get("success", False):
                self.ai_status_label.config(
                    text="AI Status: ✅ Available (Local)",
                    fg=self.colors['success']
                )
                self.ai_available = True
            else:
                self.ai_status_label.config(
                    text="AI Status: ❌ Ollama not running",
                    fg=self.colors['danger']
                )
                self.ai_available = False
                
        except ImportError:
            self.ai_status_label.config(
                text="AI Status: ❌ AI components not installed",
                fg=self.colors['danger']
            )
            self.ai_available = False
        except Exception as e:
            self.ai_status_label.config(
                text="AI Status: ❌ Connection failed",
                fg=self.colors['danger']
            )
            self.ai_available = False
            logger.warning(f"AI availability check failed: {e}")
    
    def _on_ai_toggle(self):
        """Handle AI validation toggle"""
        if self.ai_validation_var.get() and not getattr(self, 'ai_available', False):
            # User enabled AI but it's not available
            messagebox.showwarning(
                "AI Validation Unavailable",
                "AI validation is not available. Please ensure:\n\n"
                "1. Ollama is installed and running\n"
                "2. Run: ollama pull llama3.1:8b\n"
                "3. Start Ollama server: ollama serve\n\n"
                "Continuing with standard conversion..."
            )
            self.ai_validation_var.set(False)
        
        # Save settings when toggled
        self.save_user_settings()
    
    def create_widgets(self):
        """Create and arrange all GUI widgets with modern design"""
        # Main container with padding
        main_container = tk.Frame(self.root, bg='#f8f9fa')
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header section
        self.create_header_section(main_container)
        
        # Main content area with three columns
        content_frame = tk.Frame(main_container, bg='#f8f9fa')
        content_frame.pack(fill='both', expand=True, pady=(20, 0))
        
        # Left column - File Selection
        left_column = tk.Frame(content_frame, bg='#f8f9fa')
        left_column.pack(side='left', fill='both', expand=True, padx=(0, 10))
        self.create_file_selection_column(left_column)
        
        # Middle column - Processing & Progress
        middle_column = tk.Frame(content_frame, bg='#f8f9fa')
        middle_column.pack(side='left', fill='both', expand=True, padx=10)
        self.create_processing_column(middle_column)
        
        # Right column - Output Settings
        right_column = tk.Frame(content_frame, bg='#f8f9fa')
        right_column.pack(side='left', fill='both', expand=True, padx=(10, 0))
        self.create_output_settings_column(right_column)
        
        # Bottom section - Status and Actions
        self.create_bottom_section(main_container)
    
    def create_header_section(self, parent):
        """Create modern header with title and description"""
        header_frame = tk.Frame(parent, bg='white', height=100)
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # Add subtle border
        border_frame = tk.Frame(header_frame, bg=self.colors['border'], height=1)
        border_frame.pack(side='bottom', fill='x')
        
        # Header content
        header_content = tk.Frame(header_frame, bg='white')
        header_content.pack(fill='both', expand=True, padx=30, pady=20)
        
        # Title
        title_label = tk.Label(header_content,
                              text="PDF to Excel Converter",
                              font=('Segoe UI', 24, 'bold'),
                              fg=self.colors['dark'],
                              bg='white')
        title_label.pack(anchor='w')
        
        # Subtitle
        subtitle_label = tk.Label(header_content,
                                 text="Transform real estate PDF listings into structured Excel spreadsheets",
                                 font=('Segoe UI', 12),
                                 fg=self.colors['secondary'],
                                 bg='white')
        subtitle_label.pack(anchor='w', pady=(5, 0))
    
    def create_file_selection_column(self, parent):
        """Create file selection section with drag-and-drop"""
        # Card frame
        card_frame = self.create_card_frame(parent, "📁 Select PDF Files")
        card_frame.pack(fill='both', expand=True)
        
        # Drag and drop area
        self.create_drag_drop_area(card_frame)
        
        # Action buttons
        button_frame = tk.Frame(card_frame, bg='white')
        button_frame.pack(fill='x', pady=(15, 0))
        
        # Select files button
        select_files_btn = tk.Button(button_frame,
                                   text="Browse Files",
                                   command=self.select_files,
                                   bg=self.colors['primary'],
                                   fg='white',
                                   font=('Segoe UI', 10, 'bold'),
                                   relief='flat',
                                   padx=20,
                                   pady=8,
                                   cursor='hand2')
        select_files_btn.pack(side='left', padx=(0, 10))
        
        # Select folder button
        select_folder_btn = tk.Button(button_frame,
                                    text="Browse Folder",
                                    command=self.select_folder,
                                    bg=self.colors['secondary'],
                                    fg='white',
                                    font=('Segoe UI', 10),
                                    relief='flat',
                                    padx=20,
                                    pady=8,
                                    cursor='hand2')
        select_folder_btn.pack(side='left')
        
        # File list
        self.create_file_list(card_frame)
    
    def create_processing_column(self, parent):
        """Create processing and progress section"""
        # Card frame
        card_frame = self.create_card_frame(parent, "⚡ Processing")
        card_frame.pack(fill='both', expand=True)
        
        # Conversion button
        self.convert_btn = tk.Button(card_frame,
                                   text="🚀 Convert to Excel",
                                   command=self.start_conversion,
                                   bg=self.colors['success'],
                                   fg='white',
                                   font=('Segoe UI', 14, 'bold'),
                                   relief='flat',
                                   padx=30,
                                   pady=15,
                                   cursor='hand2')
        self.convert_btn.pack(pady=(0, 20))
        
        # Progress section
        progress_frame = tk.Frame(card_frame, bg='white')
        progress_frame.pack(fill='x', pady=(0, 20))
        
        # Progress label
        self.progress_label = tk.Label(progress_frame,
                                     text="Ready to convert",
                                     font=('Segoe UI', 11),
                                     fg=self.colors['dark'],
                                     bg='white')
        self.progress_label.pack(anchor='w')
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame,
                                          variable=self.progress_var,
                                          maximum=100,
                                          style='Modern.Horizontal.TProgressbar')
        self.progress_bar.pack(fill='x', pady=(10, 0))
        
        # Processing stats
        self.create_processing_stats(card_frame)
        
        # Cancel button
        self.cancel_btn = tk.Button(card_frame,
                                  text="❌ Cancel",
                                  command=self.cancel_conversion,
                                  bg=self.colors['danger'],
                                  fg='white',
                                  font=('Segoe UI', 10),
                                  relief='flat',
                                  padx=20,
                                  pady=8,
                                  cursor='hand2',
                                  state='disabled')
        self.cancel_btn.pack(pady=(10, 0))
    
    def create_output_settings_column(self, parent):
        """Create enhanced output settings section"""
        # Card frame
        card_frame = self.create_card_frame(parent, "⚙️ Output Settings")
        card_frame.pack(fill='both', expand=True)
        
        # Output format selection
        format_frame = tk.Frame(card_frame, bg='white')
        format_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(format_frame, text="Output Format:", 
                font=('Segoe UI', 10, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w')
        
        self.format_var = tk.StringVar(value=self.settings['preferred_format'])
        format_combo = ttk.Combobox(format_frame, textvariable=self.format_var,
                                  values=['xlsx', 'csv', 'json'],
                                  state='readonly', width=20)
        format_combo.pack(anchor='w', pady=(5, 0))
        
        # Output location
        location_frame = tk.Frame(card_frame, bg='white')
        location_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(location_frame, text="Output Location:", 
                font=('Segoe UI', 10, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w')
        
        location_input_frame = tk.Frame(location_frame, bg='white')
        location_input_frame.pack(fill='x', pady=(5, 0))
        
        self.output_var = tk.StringVar(value=self.settings['last_output_directory'])
        output_entry = tk.Entry(location_input_frame, textvariable=self.output_var,
                              font=('Segoe UI', 9), relief='solid', bd=1)
        output_entry.pack(side='left', fill='x', expand=True, padx=(0, 5))
        
        browse_btn = tk.Button(location_input_frame, text="Browse",
                             command=self.browse_output_location,
                             bg=self.colors['secondary'], fg='white',
                             font=('Segoe UI', 9), relief='flat',
                             padx=15, pady=5, cursor='hand2')
        browse_btn.pack(side='right')
        
        # Filename pattern
        filename_frame = tk.Frame(card_frame, bg='white')
        filename_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(filename_frame, text="Filename Pattern:", 
                font=('Segoe UI', 10, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w')
        
        self.filename_var = tk.StringVar(value=self.settings['output_filename_pattern'])
        filename_entry = tk.Entry(filename_frame, textvariable=self.filename_var,
                                font=('Segoe UI', 9), relief='solid', bd=1)
        filename_entry.pack(fill='x', pady=(5, 0))
        
        # Help text for filename pattern
        help_label = tk.Label(filename_frame, 
                            text="Use {date}, {time} for dynamic naming",
                            font=('Segoe UI', 8),
                            fg=self.colors['secondary'], bg='white')
        help_label.pack(anchor='w', pady=(2, 0))
        
        # Processing options
        self.create_processing_options(card_frame)
        
        # Advanced settings button
        advanced_btn = tk.Button(card_frame,
                               text="Advanced Settings",
                               command=self.show_advanced_settings,
                               bg=self.colors['light'],
                               fg=self.colors['dark'],
                               font=('Segoe UI', 9),
                               relief='solid',
                               bd=1,
                               padx=15,
                               pady=5,
                               cursor='hand2')
        advanced_btn.pack(pady=(15, 0))
    
    def create_card_frame(self, parent, title):
        """Create a card-style frame with title"""
        # Main card frame
        card = tk.Frame(parent, bg='white', relief='solid', bd=1)
        
        # Title bar
        title_frame = tk.Frame(card, bg=self.colors['light'], height=40)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text=title,
                             font=('Segoe UI', 12, 'bold'),
                             bg=self.colors['light'],
                             fg=self.colors['dark'])
        title_label.pack(side='left', padx=15, pady=10)
        
        # Content area
        content_frame = tk.Frame(card, bg='white')
        content_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        return content_frame
    
    def create_drag_drop_area(self, parent):
        """Create drag and drop area for files"""
        drop_frame = tk.Frame(parent, bg='#f8fafc', relief='dashed', bd=2)
        drop_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # Drop area content
        drop_content = tk.Frame(drop_frame, bg='#f8fafc')
        drop_content.pack(expand=True, fill='both')
        
        # Icon and text
        icon_label = tk.Label(drop_content, text="📄",
                            font=('Segoe UI', 48),
                            bg='#f8fafc', fg=self.colors['secondary'])
        icon_label.pack(pady=(30, 10))
        
        text_label = tk.Label(drop_content,
                            text="Drag & Drop PDF files here\nor use the buttons below",
                            font=('Segoe UI', 11),
                            bg='#f8fafc', fg=self.colors['secondary'],
                            justify='center')
        text_label.pack(pady=(0, 30))
        
        # Bind drag and drop events (basic implementation)
        self.setup_drag_drop(drop_frame)
    
    def create_file_list(self, parent):
        """Create modern file list with icons"""
        list_frame = tk.Frame(parent, bg='white')
        list_frame.pack(fill='both', expand=True, pady=(15, 0))
        
        # List header
        header_frame = tk.Frame(list_frame, bg=self.colors['light'])
        header_frame.pack(fill='x')
        
        tk.Label(header_frame, text="Selected Files",
                font=('Segoe UI', 10, 'bold'),
                bg=self.colors['light'], fg=self.colors['dark']).pack(side='left', padx=10, pady=5)
        
        # Clear button
        clear_btn = tk.Button(header_frame, text="Clear All",
                            command=self.clear_files,
                            bg=self.colors['danger'], fg='white',
                            font=('Segoe UI', 8), relief='flat',
                            padx=10, pady=2, cursor='hand2')
        clear_btn.pack(side='right', padx=5, pady=3)
        
        # Scrollable list
        list_container = tk.Frame(list_frame, bg='white')
        list_container.pack(fill='both', expand=True)
        
        # Listbox with scrollbar
        self.file_listbox = tk.Listbox(list_container,
                                      font=('Segoe UI', 9),
                                      relief='flat',
                                      selectbackground=self.colors['primary'],
                                      selectforeground='white',
                                      bg='white',
                                      fg=self.colors['dark'],
                                      borderwidth=0,
                                      highlightthickness=0)
        
        scrollbar = ttk.Scrollbar(list_container, orient='vertical', command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.file_listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
    
    def create_processing_stats(self, parent):
        """Create processing statistics display"""
        stats_frame = tk.Frame(parent, bg='white')
        stats_frame.pack(fill='x', pady=(20, 0))
        
        # Stats grid
        stats_grid = tk.Frame(stats_frame, bg='white')
        stats_grid.pack(fill='x')
        
        # Configure grid
        for i in range(3):
            stats_grid.columnconfigure(i, weight=1)
        
        # Files stat
        files_frame = self.create_stat_box(stats_grid, "Files", "0", self.colors['primary'])
        files_frame.grid(row=0, column=0, padx=(0, 5), sticky='ew')
        self.files_stat_label = files_frame.children['!label2']
        
        # Records stat  
        records_frame = self.create_stat_box(stats_grid, "Records", "0", self.colors['success'])
        records_frame.grid(row=0, column=1, padx=5, sticky='ew')
        self.records_stat_label = records_frame.children['!label2']
        
        # Errors stat
        errors_frame = self.create_stat_box(stats_grid, "Errors", "0", self.colors['danger'])
        errors_frame.grid(row=0, column=2, padx=(5, 0), sticky='ew')
        self.errors_stat_label = errors_frame.children['!label2']
    
    def create_stat_box(self, parent, label, value, color):
        """Create a statistics box"""
        frame = tk.Frame(parent, bg=color, relief='flat')
        
        # Value
        value_label = tk.Label(frame, text=value,
                             font=('Segoe UI', 16, 'bold'),
                             bg=color, fg='white')
        value_label.pack(pady=(10, 2))
        
        # Label
        label_label = tk.Label(frame, text=label,
                             font=('Segoe UI', 9),
                             bg=color, fg='white')
        label_label.pack(pady=(0, 10))
        
        return frame
    
    def create_processing_options(self, parent):
        """Create processing options checkboxes"""
        options_frame = tk.Frame(parent, bg='white')
        options_frame.pack(fill='x', pady=(15, 0))
        
        tk.Label(options_frame, text="Processing Options:", 
                font=('Segoe UI', 10, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w')
        
        # Checkboxes
        self.include_summary_var = tk.BooleanVar(value=self.settings['include_summary_sheet'])
        summary_check = tk.Checkbutton(options_frame, text="Include summary sheet",
                                     variable=self.include_summary_var,
                                     font=('Segoe UI', 9),
                                     bg='white', fg=self.colors['dark'])
        summary_check.pack(anchor='w', pady=(5, 2))
        
        self.category_sheets_var = tk.BooleanVar(value=self.settings['create_category_sheets'])
        category_check = tk.Checkbutton(options_frame, text="Create category sheets",
                                      variable=self.category_sheets_var,
                                      font=('Segoe UI', 9),
                                      bg='white', fg=self.colors['dark'])
        category_check.pack(anchor='w', pady=2)
        
        self.auto_open_var = tk.BooleanVar(value=self.settings['auto_open_result'])
        auto_open_check = tk.Checkbutton(options_frame, text="Auto-open result file",
                                       variable=self.auto_open_var,
                                       font=('Segoe UI', 9),
                                       bg='white', fg=self.colors['dark'])
        auto_open_check.pack(anchor='w', pady=2)
        
        # AI Validation option
        self.ai_validation_var = tk.BooleanVar(value=self.settings.get('ai_validation_enabled', False))
        ai_validation_check = tk.Checkbutton(options_frame, text="🤖 Enable AI validation (95% accuracy)",
                                           variable=self.ai_validation_var,
                                           font=('Segoe UI', 9, 'bold'),
                                           bg='white', fg=self.colors['primary'],
                                           command=self._on_ai_toggle)
        ai_validation_check.pack(anchor='w', pady=2)
        
        # AI Status indicator
        self.ai_status_label = tk.Label(options_frame, 
                                      text="AI Status: Checking...",
                                      font=('Segoe UI', 8),
                                      bg='white', fg=self.colors['secondary'])
        self.ai_status_label.pack(anchor='w', padx=(20, 0), pady=(0, 2))
        
        # Check AI availability after UI setup
        self.root.after(100, self._check_ai_availability)
    
    def create_bottom_section(self, parent):
        """Create bottom status section"""
        bottom_frame = tk.Frame(parent, bg='white', height=200)
        bottom_frame.pack(fill='x', pady=(20, 0))
        
        # Status header
        status_header = tk.Frame(bottom_frame, bg=self.colors['light'])
        status_header.pack(fill='x')
        
        tk.Label(status_header, text="📋 Status Log",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['light'], fg=self.colors['dark']).pack(side='left', padx=15, pady=10)
        
        # Status text area
        status_container = tk.Frame(bottom_frame, bg='white')
        status_container.pack(fill='both', expand=True, padx=15, pady=15)
        
        # Text widget with scrollbar
        self.status_text = tk.Text(status_container,
                                 height=8,
                                 font=('Consolas', 9),
                                 bg='#f8fafc',
                                 fg=self.colors['dark'],
                                 relief='flat',
                                 borderwidth=0,
                                 wrap='word')
        
        status_scrollbar = ttk.Scrollbar(status_container, orient='vertical', command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=status_scrollbar.set)
        
        self.status_text.pack(side='left', fill='both', expand=True)
        status_scrollbar.pack(side='right', fill='y')
        
        # Configure text tags for colored messages
        self.status_text.tag_configure("info", foreground=self.colors['dark'])
        self.status_text.tag_configure("success", foreground=self.colors['success'])
        self.status_text.tag_configure("warning", foreground=self.colors['warning'])
        self.status_text.tag_configure("error", foreground=self.colors['danger'])
        
        # Initial welcome message
        self.add_status_message("Welcome to PDF to Excel Converter! Select your PDF files to begin.", "info")
    
    def setup_drag_drop(self, drop_area):
        """Setup basic drag and drop functionality"""
        # Note: Full drag-and-drop would require additional libraries
        # This is a basic implementation
        def on_drop_enter(event):
            drop_area.configure(bg='#e0f2fe')
        
        def on_drop_leave(event):
            drop_area.configure(bg='#f8fafc')
        
        drop_area.bind('<Enter>', on_drop_enter)
        drop_area.bind('<Leave>', on_drop_leave)
    
    # File selection methods
    def select_files(self):
        """Enhanced file selection with filters"""
        filetypes = [
            ('PDF files', '*.pdf'),
            ('All files', '*.*')
        ]
        
        files = filedialog.askopenfilenames(
            title="Select PDF Files",
            filetypes=filetypes,
            initialdir=self.settings['last_output_directory']
        )
        
        if files:
            self.selected_files.extend(files)
            self.update_file_list()
            self.update_stats()
            self.add_status_message(f"Added {len(files)} PDF file(s)", "info")
    
    def select_folder(self):
        """Enhanced folder selection"""
        folder = filedialog.askdirectory(
            title="Select Folder with PDF Files",
            initialdir=self.settings['last_output_directory']
        )
        
        if folder:
            pdf_files = glob.glob(os.path.join(folder, "*.pdf"))
            
            if pdf_files:
                self.selected_files.extend(pdf_files)
                self.update_file_list()
                self.update_stats()
                self.add_status_message(f"Found {len(pdf_files)} PDF file(s) in folder", "info")
            else:
                messagebox.showwarning("No PDF Files", "No PDF files found in the selected folder.")
    
    def clear_files(self):
        """Clear all selected files"""
        self.selected_files.clear()
        self.update_file_list()
        self.update_stats()
        self.add_status_message("File selection cleared", "info")
    
    def update_file_list(self):
        """Update the file listbox with modern styling"""
        self.file_listbox.delete(0, tk.END)
        for file_path in self.selected_files:
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) / 1024  # KB
            display_text = f"📄 {filename} ({file_size:.1f} KB)"
            self.file_listbox.insert(tk.END, display_text)
    
    def update_stats(self):
        """Update processing statistics"""
        self.files_stat_label.configure(text=str(len(self.selected_files)))
    
    def browse_output_location(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(
            title="Select Output Directory",
            initialdir=self.settings['last_output_directory']
        )
        
        if directory:
            self.output_var.set(directory)
            self.settings['last_output_directory'] = directory
    
    def show_advanced_settings(self):
        """Show advanced settings dialog"""
        # This would open a separate dialog for advanced settings
        messagebox.showinfo("Advanced Settings", "Advanced settings dialog would open here.")
    
    def add_status_message(self, message: str, message_type: str = "info"):
        """Add message to status text with modern formatting"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # Insert message with timestamp and color
        self.status_text.insert(tk.END, f"[{timestamp}] {message}\n", message_type)
        self.status_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_progress(self, current: int, total: int, message: str = ""):
        """Update progress bar and statistics"""
        if total > 0:
            progress_percent = (current / total) * 100
            self.progress_var.set(progress_percent)
        
        if message:
            self.progress_label.config(text=message)
        
        self.root.update_idletasks()
    
    def start_conversion(self):
        """Start the conversion process with enhanced validation"""
        if not self.selected_files:
            messagebox.showwarning("No Files Selected", "Please select PDF files to convert.")
            return
        
        # Validate output settings
        output_dir = self.output_var.get().strip()
        if not output_dir or not os.path.exists(output_dir):
            messagebox.showerror("Invalid Output Directory", "Please select a valid output directory.")
            return
        
        # Generate output filename
        pattern = self.filename_var.get()
        current_time = datetime.datetime.now()
        filename = pattern.format(
            date=current_time.strftime("%Y%m%d"),
            time=current_time.strftime("%H%M%S")
        )
        
        if not filename.endswith('.xlsx'):
            filename += '.xlsx'
        
        output_path = os.path.join(output_dir, filename)
        
        # Update UI state
        self.convert_btn.config(state='disabled', bg=self.colors['secondary'])
        self.cancel_btn.config(state='normal')
        
        # Reset progress
        self.progress_var.set(0)
        self.add_status_message("Starting conversion process...", "info")
        
        # Save settings
        self.save_user_settings()
        
        # Start conversion in separate thread
        self.conversion_thread = threading.Thread(
            target=self._run_conversion,
            args=(self.selected_files, output_path),
            daemon=True
        )
        self.conversion_thread.start()
    
    def _run_conversion(self, files: List[str], output_path: str):
        """Run conversion in separate thread"""
        try:
            # Check if AI validation is enabled
            use_ai_validation = self.ai_validation_var.get() and getattr(self, 'ai_available', False)
            
            if use_ai_validation:
                self.add_status_message("🤖 AI validation enabled - Enhanced processing...", "info")
                self._run_ai_enhanced_conversion(files, output_path)
            elif self.conversion_callback:
                self.add_status_message("📄 Standard conversion processing...", "info")
                self.conversion_callback(files, output_path, self.update_progress, self.add_status_message)
                
                # Auto-open file if enabled
                if self.auto_open_var.get() and os.path.exists(output_path):
                    os.startfile(output_path)  # Windows
            else:
                self.add_status_message("No conversion callback set", "error")
                
        except Exception as e:
            self.add_status_message(f"Conversion failed: {str(e)}", "error")
        finally:
            # Re-enable buttons
            self.root.after(0, self._reset_buttons)
    
    def _run_ai_enhanced_conversion(self, files: List[str], output_path: str):
        """Run AI-enhanced conversion"""
        try:
            from enhanced_main import EnhancedPDFConverter
            
            # Initialize enhanced converter
            converter = EnhancedPDFConverter(
                model=self.settings['ai_model'],
                auto_correct=self.settings['ai_auto_correct']
            )
            
            total_files = len(files)
            successful_conversions = 0
            
            for i, pdf_file in enumerate(files, 1):
                self.update_progress(i, total_files, f"Processing {os.path.basename(pdf_file)} with AI...")
                self.add_status_message(f"🔄 AI Processing: {os.path.basename(pdf_file)}", "info")
                
                try:
                    # For multiple files, create individual output files
                    if total_files > 1:
                        file_output_dir = os.path.dirname(output_path)
                        base_name = os.path.splitext(os.path.basename(pdf_file))[0]
                        individual_output = os.path.join(file_output_dir, f"{base_name}_AI_enhanced.xlsx")
                    else:
                        individual_output = output_path
                    
                    # Run enhanced conversion
                    results = converter.convert_with_validation(
                        pdf_path=pdf_file,
                        output_dir=os.path.dirname(individual_output)
                    )
                    
                    if results.get('success'):
                        successful_conversions += 1
                        
                        # Update UI with results
                        summary = results.get('summary', {})
                        accuracy = summary.get('accuracy', {})
                        
                        self.add_status_message(
                            f"✅ {os.path.basename(pdf_file)}: {accuracy.get('match_rate', 0):.1f}% accuracy, "
                            f"{accuracy.get('records_captured', 0)} records captured", 
                            "success"
                        )
                        
                        # Update stats
                        if hasattr(self, 'records_stat_label'):
                            self.records_stat_label.config(text=str(accuracy.get('records_captured', 0)))
                        
                        # Show AI improvements
                        improvements = summary.get('improvements', {})
                        if improvements.get('corrections_applied', 0) > 0:
                            self.add_status_message(
                                f"🤖 AI applied {improvements['corrections_applied']} corrections automatically",
                                "success"
                            )
                    else:
                        self.add_status_message(f"❌ Failed to process {os.path.basename(pdf_file)}: {results.get('error', 'Unknown error')}", "error")
                        
                except Exception as file_error:
                    self.add_status_message(f"❌ Error processing {os.path.basename(pdf_file)}: {str(file_error)}", "error")
            
            # Final summary
            self.add_status_message(
                f"🎉 AI Enhanced conversion completed: {successful_conversions}/{total_files} files processed successfully",
                "success"
            )
            
            # Auto-open file if enabled and single file
            if self.auto_open_var.get() and total_files == 1 and os.path.exists(output_path):
                os.startfile(output_path)
                
        except ImportError:
            self.add_status_message("❌ AI components not available. Install enhanced validation system.", "error")
        except Exception as e:
            self.add_status_message(f"❌ AI conversion failed: {str(e)}", "error")
    
    def _reset_buttons(self):
        """Reset button states"""
        self.convert_btn.config(state='normal', bg=self.colors['success'])
        self.cancel_btn.config(state='disabled')
    
    def cancel_conversion(self):
        """Cancel the conversion process"""
        self.add_status_message("Conversion cancelled by user", "warning")
        self._reset_buttons()
    
    def set_conversion_callback(self, callback: Callable):
        """Set the callback function for conversion"""
        self.conversion_callback = callback
    
    def run(self):
        """Start the GUI event loop"""
        try:
            # Apply window geometry from settings
            self.root.geometry(self.settings.get('window_geometry', '1200x800'))
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("GUI closed by user")
        finally:
            # Save settings on exit
            self.settings['window_geometry'] = self.root.geometry()
            self.save_user_settings()

# Example usage
if __name__ == "__main__":
    app = ModernPDFConverterGUI()
    
    def dummy_conversion(files, output_path, progress_callback, status_callback):
        """Dummy conversion function for testing"""
        import time
        
        total_files = len(files)
        for i, file in enumerate(files, 1):
            status_callback(f"Processing: {os.path.basename(file)}")
            progress_callback(i, total_files, f"Processing file {i} of {total_files}")
            time.sleep(1)
        
        status_callback("Conversion completed successfully!", "success")
        progress_callback(total_files, total_files, "Conversion complete")
    
    app.set_conversion_callback(dummy_conversion)
    app.run() 