#!/usr/bin/env python3
"""
Complete debug flow: generate Excel and immediately verify what was actually written
"""

import sys
import os
sys.path.append('.')

from main import PDFToExcelConverter
import pandas as pd

def debug_complete_flow():
    """Debug the complete flow from DataFrame to Excel file"""
    
    print("🔍 COMPLETE FLOW DEBUG: DATAFRAME → EXCEL → VERIFICATION")
    print("=" * 70)
    
    # Initialize converter
    converter = PDFToExcelConverter()
    converter.data_processor.set_debug_mode(True)
    
    # Process the PDF to get DataFrame
    pdf_files = ['pdfs/KOTHI SALE JULY-2025.pdf']
    
    # Extract text
    extracted_texts = converter.pdf_parser.extract_from_multiple_pdfs(pdf_files)
    
    if not extracted_texts:
        print("❌ Failed to extract text")
        return
    
    # Process data to get DataFrame
    df = converter.data_processor.process_multiple_files(extracted_texts)
    
    print(f"📊 STEP 1: Original DataFrame")
    print(f"   Shape: {df.shape}")
    print(f"   Columns: {list(df.columns)}")
    
    # Analyze the DataFrame before Excel generation
    if '_incomplete' in df.columns:
        incomplete_mask = df['_incomplete'] == 'true'
        incomplete_count = incomplete_mask.sum()
        main_count = len(df) - incomplete_count
        
        print(f"   📊 Incomplete records: {incomplete_count}")
        print(f"   📊 Main records: {main_count}")
        print(f"   📊 Total: {len(df)}")
        
        # Check main records for 7 empty fields
        main_df = df[~incomplete_mask].copy()
        key_fields = ['Property Code', 'Size/Yards', 'Contact Person', 'Phone Numbers', 'Property Type', 'Details', 'Status']
        
        count_with_7_empty = 0
        for i, row in main_df.iterrows():
            empty_count = sum(1 for field in key_fields 
                            if pd.isna(row[field]) or row[field] == '' or str(row[field]) == 'nan')
            if empty_count == 7:
                count_with_7_empty += 1
        
        print(f"   📊 Main records with 7 empty fields: {count_with_7_empty}")
        print()
    
    # STEP 2: Generate Excel file
    print(f"📊 STEP 2: Generating Excel file...")
    output_path = "Real_Estate_Data_DEBUG.xlsx"
    
    # Delete existing file if it exists
    if os.path.exists(output_path):
        os.remove(output_path)
        print(f"   🗑️  Deleted existing file: {output_path}")
    
    # Generate Excel
    excel_file_path = converter.excel_generator.create_excel_file(
        df, 
        output_path,
        include_summary=True,
        create_category_sheets=True,
        include_incomplete_records=True
    )
    
    print(f"   ✅ Excel file created: {excel_file_path}")
    print(f"   📁 File exists: {os.path.exists(excel_file_path)}")
    print()
    
    # STEP 3: Immediately read back the Excel file
    print(f"📊 STEP 3: Reading back Excel file...")
    
    if os.path.exists(excel_file_path):
        # Read main records
        df_main_excel = pd.read_excel(excel_file_path, sheet_name='All Property Data')
        print(f"   📊 Main records in Excel: {len(df_main_excel)}")
        
        # Count records with 7 empty fields
        count_7_empty_excel = 0
        for i, row in df_main_excel.iterrows():
            empty_count = sum(1 for field in key_fields 
                            if pd.isna(row[field]) or row[field] == '' or str(row[field]) == 'nan')
            if empty_count == 7:
                count_7_empty_excel += 1
        
        print(f"   📊 Main records with 7 empty fields in Excel: {count_7_empty_excel}")
        
        # Read incomplete records
        df_incomplete_excel = pd.read_excel(excel_file_path, sheet_name='Incomplete Records')
        print(f"   📊 Incomplete records in Excel: {len(df_incomplete_excel)}")
        print()
        
        # STEP 4: Compare expected vs actual
        print(f"📊 STEP 4: COMPARISON")
        print(f"   Expected main records: {main_count}")
        print(f"   Actual main records in Excel: {len(df_main_excel)}")
        print(f"   ✅ Main records match: {main_count == len(df_main_excel)}")
        print()
        print(f"   Expected incomplete records: {incomplete_count}")
        print(f"   Actual incomplete records in Excel: {len(df_incomplete_excel)}")
        print(f"   ✅ Incomplete records match: {incomplete_count == len(df_incomplete_excel)}")
        print()
        print(f"   Expected main records with 7 empty fields: 0")
        print(f"   Actual main records with 7 empty fields: {count_7_empty_excel}")
        print(f"   ✅ No 7-empty records in main sheet: {count_7_empty_excel == 0}")
        print()
        
        # Overall result
        if (main_count == len(df_main_excel) and 
            incomplete_count == len(df_incomplete_excel) and 
            count_7_empty_excel == 0):
            print("🎉 SUCCESS: Excel file is correctly generated!")
            print("✅ The broken records classifier is working perfectly!")
        else:
            print("❌ ISSUE: There's a mismatch between expected and actual results")
            print("🔍 This indicates a bug in the Excel generation process")
            
            # Debug the specific issue
            if main_count != len(df_main_excel):
                print(f"   🔍 Main records mismatch: Expected {main_count}, got {len(df_main_excel)}")
            if incomplete_count != len(df_incomplete_excel):
                print(f"   🔍 Incomplete records mismatch: Expected {incomplete_count}, got {len(df_incomplete_excel)}")
            if count_7_empty_excel > 0:
                print(f"   🔍 Found {count_7_empty_excel} records with 7 empty fields in main sheet")
        
    else:
        print("❌ Excel file was not created successfully")
    
    # Clean up debug file
    if os.path.exists(output_path):
        os.remove(output_path)
        print(f"\n🗑️  Cleaned up debug file: {output_path}")

if __name__ == "__main__":
    debug_complete_flow() 