#!/usr/bin/env python3
"""
Test AI validation functionality
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_validation():
    """Test AI validation is properly configured"""
    print("Testing AI validation setup...")
    
    try:
        from gui_simple import SimplifiedModernGUI
        import tkinter as tk
        
        # Create GUI
        root = tk.Tk()
        gui = SimplifiedModernGUI()
        
        # Test AI validation enabled by default
        ai_enabled = gui.ai_validation_var.get()
        print(f"[INFO] AI validation default state: {ai_enabled}")
        
        if ai_enabled:
            print("[OK] AI validation is enabled by default")
        else:
            print("[ERROR] AI validation should be enabled by default")
            return False
        
        # Test AI availability check
        ai_available = gui._check_ai_availability()
        print(f"[INFO] AI availability: {ai_available}")
        
        if ai_available:
            print("[OK] AI (Ollama) is available and ready")
        else:
            print("[WARNING] AI not available - Ollama may not be running")
        
        # Test the enhanced conversion import
        try:
            from enhanced_main import EnhancedPDFConverter
            print("[OK] Enhanced PDF converter can be imported")
            
            # Test creating the converter
            converter = EnhancedPDFConverter()
            print("[OK] Enhanced PDF converter created successfully")
            
        except ImportError as e:
            print(f"[ERROR] Cannot import enhanced converter: {e}")
            return False
        except Exception as e:
            print(f"[WARNING] Enhanced converter creation failed: {e}")
        
        # Clean up
        root.destroy()
        
        print("\n[SUCCESS] AI validation setup is working")
        return True
        
    except Exception as e:
        print(f"[ERROR] AI validation test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ai_validation()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)