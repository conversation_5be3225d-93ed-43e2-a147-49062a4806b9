"""
Enhanced PDF to Excel Converter with AI Validation
Integrates all validation components for 95% accuracy target
"""

import json
import logging
import time
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Any

from smart_validator import create_smart_validator
from quality_assurance import QualityAssuranceSystem
from ollama_client import create_ollama_client

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedPDFConverter:
    """
    Enhanced PDF to Excel converter with AI validation and quality assurance
    """
    
    def __init__(self, model: str = "llama3.1:8b", auto_correct: bool = True):
        self.model = model
        self.auto_correct = auto_correct
        
        # Defer AI component initialization to avoid long startup times
        self.validator = None
        self.qa_system = None
        self.ai_available = None
        
        logger.info(f"🚀 Enhanced PDF Converter initialized (fast mode)")
        logger.info(f"   Model: {model}")
        logger.info(f"   Auto-correct: {auto_correct}")
        logger.info(f"   AI components will be initialized on first use")
    
    def _initialize_ai_components(self):
        """Initialize AI components on first use"""
        if self.validator is None:
            logger.info("🔄 Initializing AI validation components...")
            try:
                self.validator = create_smart_validator(model=self.model, auto_correct=self.auto_correct)
                self.qa_system = QualityAssuranceSystem()
                self.ai_available = True
                logger.info("✅ AI components initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize AI components: {str(e)}")
                self.ai_available = False
    
    def _check_ai_availability(self) -> bool:
        """Check if Ollama AI is available (quick check)"""
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=3)
            if response.status_code == 200:
                models = response.json().get('models', [])
                for model in models:
                    if 'llama3.1:8b' in model.get('name', ''):
                        return True
            return False
        except Exception as e:
            logger.warning(f"⚠️ AI not available: {str(e)}")
            return False
    
    def convert_with_validation(self, pdf_path: str, output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert PDF to Excel with comprehensive validation and quality assurance
        
        Args:
            pdf_path: Path to PDF file
            output_dir: Output directory (optional)
            
        Returns:
            Comprehensive conversion results
        """
        start_time = time.time()
        pdf_path = Path(pdf_path)
        
        if not pdf_path.exists():
            return {"error": f"PDF file not found: {pdf_path}"}
        
        logger.info(f"🔄 Starting enhanced conversion: {pdf_path.name}")
        
        try:
            # Step 1: Run original conversion
            logger.info("📄 Step 1: Running PDF to Excel conversion...")
            excel_path = self._run_original_conversion(pdf_path, output_dir)
            
            if not excel_path or not Path(excel_path).exists():
                return {"error": "PDF to Excel conversion failed"}
            
            # Step 2: Initialize and run AI validation
            logger.info("🔍 Step 2: Initializing AI validation...")
            self._initialize_ai_components()
            
            if self.ai_available and self.validator:
                logger.info("🤖 Running AI validation...")
                corrected_path = None
                if output_dir:
                    corrected_path = str(Path(output_dir) / f"validated_{pdf_path.stem}.xlsx")
                
                validation_results = self.validator.validate_pdf_to_excel(
                    pdf_path=str(pdf_path),
                    excel_path=excel_path,
                    output_path=corrected_path
                )
                
                # Step 3: Quality assessment
                logger.info("📊 Step 3: Quality assessment...")
                qa_session = self.qa_system.record_validation_session(
                    validation_results, str(pdf_path), excel_path
                )
            else:
                logger.warning("⚠️ AI validation skipped - components not available")
                validation_results = {"validation_report": None, "corrections_applied": 0}
                qa_session = None
            
            # Step 4: Generate comprehensive report
            processing_time = time.time() - start_time
            
            results = {
                "success": True,
                "files": {
                    "source_pdf": str(pdf_path),
                    "original_excel": excel_path,
                    "validated_excel": corrected_path if corrected_path and Path(corrected_path).exists() else None,
                },
                "validation": validation_results,
                "quality_session": qa_session.session_id if qa_session else None,
                "processing_time": processing_time,
                "ai_used": self.ai_available,
                "summary": self._generate_conversion_summary(validation_results, qa_session)
            }
            
            logger.info(f"✅ Enhanced conversion completed in {processing_time:.2f}s")
            return results
            
        except Exception as e:
            logger.error(f"❌ Enhanced conversion failed: {str(e)}")
            return {
                "error": str(e),
                "processing_time": time.time() - start_time
            }
    
    def _run_original_conversion(self, pdf_path: Path, output_dir: Optional[str]) -> Optional[str]:
        """Run the original PDF to Excel conversion using the main converter"""
        try:
            logger.info(f"🔄 Starting original conversion for: {pdf_path.name}")
            
            # Setup output path
            if output_dir:
                output_path = Path(output_dir) / f"original_{pdf_path.stem}.xlsx"
            else:
                output_path = pdf_path.parent / f"{pdf_path.stem}_converted.xlsx"
            
            logger.info(f"📁 Output path: {output_path}")
            
            # Use the main converter's proven conversion logic
            from main import PDFToExcelConverter
            converter = PDFToExcelConverter()
            
            # Create dummy callbacks for logging
            def progress_callback(current, total, message):
                logger.info(f"📊 Progress: {current}/{total} - {message}")
            
            def status_callback(message, msg_type="info"):
                logger.info(f"📝 Status: {message}")
            
            # Use the main converter's conversion method
            conversion_settings = {
                'include_summary': True,
                'create_category_sheets': True,
                'include_incomplete_records': True,
                'auto_open_result': False
            }
            
            logger.info("🔄 Running main conversion logic...")
            success = converter.convert_pdfs_to_excel(
                [str(pdf_path)],
                str(output_path),
                progress_callback,
                status_callback,
                conversion_settings
            )
            
            if success and output_path.exists():
                file_size = output_path.stat().st_size
                logger.info(f"✅ Original conversion completed: {output_path} ({file_size} bytes)")
                return str(output_path)
            else:
                logger.error("❌ Main conversion failed or file not created")
                return None
            
        except Exception as e:
            logger.error(f"❌ Original conversion failed: {str(e)}")
            import traceback
            logger.error(f"🔍 Full traceback: {traceback.format_exc()}")
            return None
    
    def _generate_conversion_summary(self, validation_results: Dict, qa_session) -> Dict[str, Any]:
        """Generate a summary of the conversion results"""
        if not validation_results.get('validation_report'):
            return {"error": "No validation report available"}
        
        report = validation_results['validation_report']
        
        summary = {
            "accuracy": {
                "match_rate": report.match_rate,
                "quality_level": self.qa_system._get_quality_level(
                    qa_session.quality_metrics.overall_quality_score if qa_session else report.match_rate
                ),
                "records_found": report.total_pdf_records,
                "records_captured": report.total_excel_records,
                "records_missing": report.total_pdf_records - report.total_excel_records
            },
            "issues": {
                "total": len(report.issues),
                "critical": len([i for i in report.issues if i.severity == 'critical']),
                "high": len([i for i in report.issues if i.severity == 'high']),
                "fixable": len([i for i in report.issues if i.suggested_fix])
            },
            "improvements": {
                "corrections_applied": validation_results.get('corrections_applied', 0),
                "ai_suggestions": len([i for i in report.issues if i.confidence > 0.7]),
                "auto_correctable": len([i for i in report.issues if i.suggested_fix and i.confidence >= 0.8])
            },
            "recommendations": validation_results.get('recommendations', [])[:3]  # Top 3
        }
        
        return summary
    
    def batch_convert(self, pdf_directory: str, output_directory: str) -> Dict[str, Any]:
        """
        Batch convert multiple PDFs with validation
        
        Args:
            pdf_directory: Directory containing PDF files
            output_directory: Directory for output files
            
        Returns:
            Batch conversion results
        """
        pdf_dir = Path(pdf_directory)
        output_dir = Path(output_directory)
        
        if not pdf_dir.exists():
            return {"error": f"PDF directory not found: {pdf_directory}"}
        
        output_dir.mkdir(exist_ok=True)
        
        # Find all PDF files
        pdf_files = list(pdf_dir.glob("*.pdf"))
        if not pdf_files:
            return {"error": f"No PDF files found in: {pdf_directory}"}
        
        logger.info(f"🔄 Starting batch conversion of {len(pdf_files)} PDFs...")
        
        results = []
        successful = 0
        failed = 0
        
        for i, pdf_path in enumerate(pdf_files, 1):
            logger.info(f"📄 Processing {i}/{len(pdf_files)}: {pdf_path.name}")
            
            try:
                result = self.convert_with_validation(str(pdf_path), str(output_dir))
                
                if result.get('success'):
                    successful += 1
                else:
                    failed += 1
                
                result['batch_index'] = i
                result['filename'] = pdf_path.name
                results.append(result)
                
            except Exception as e:
                logger.error(f"❌ Failed to process {pdf_path.name}: {str(e)}")
                failed += 1
                results.append({
                    'batch_index': i,
                    'filename': pdf_path.name,
                    'error': str(e),
                    'success': False
                })
        
        # Generate batch summary
        batch_summary = {
            "total_files": len(pdf_files),
            "successful": successful,
            "failed": failed,
            "success_rate": (successful / len(pdf_files)) * 100,
            "results": results
        }
        
        logger.info(f"✅ Batch conversion completed: {successful}/{len(pdf_files)} successful ({batch_summary['success_rate']:.1f}%)")
        
        return batch_summary
    
    def generate_quality_report(self, days: int = 30) -> str:
        """Generate and display quality report"""
        report = self.qa_system.generate_quality_report(days=days)
        
        if report.get('error'):
            return f"Quality Report Error: {report['error']}"
        
        # Format the report
        if 'summary' in report:  # Trend report
            return f"""
=== QUALITY TRENDS REPORT (Last {days} days) ===

SUMMARY:
  📊 Total Validations: {report['summary']['total_validations']}
  🎯 Average Accuracy: {report['summary']['average_accuracy']:.1f}%
  ⭐ Average Quality Score: {report['summary']['average_quality_score']:.1f}%
  ⚡ Average Processing Time: {report['summary']['average_processing_time']:.2f}s
  🤖 AI Usage Rate: {report['summary']['ai_usage_rate']:.1f}%

QUALITY DISTRIBUTION:
  🟢 Excellent (95%+): {report['quality_distribution']['excellent']}
  🔵 Good (85-95%): {report['quality_distribution']['good']}
  🟡 Acceptable (70-85%): {report['quality_distribution']['acceptable']}
  🟠 Poor (50-70%): {report['quality_distribution']['poor']}
  🔴 Unacceptable (<50%): {report['quality_distribution']['unacceptable']}

TOP ISSUES:
""" + "\n".join([f"  • {issue['type']} ({issue['severity']}): {issue['count']}" 
                for issue in report.get('top_issue_types', [])[:5]]) + f"""

RECOMMENDATIONS:
""" + "\n".join([f"  • {rec}" for rec in report.get('recommendations', [])])
        
        else:  # Session report
            return f"""
=== SESSION QUALITY REPORT ===

FILE INFO:
  📄 PDF: {report['file_info']['pdf_file']}
  📊 Excel: {report['file_info']['excel_file']}
  ⚡ Processing Time: {report['file_info']['processing_time']:.2f}s

ACCURACY:
  🎯 Match Rate: {report['accuracy_metrics']['match_rate']:.1f}%
  📊 PDF Records: {report['accuracy_metrics']['pdf_records']}
  📋 Excel Records: {report['accuracy_metrics']['excel_records']}
  ⚠️ Issues Found: {report['accuracy_metrics']['issues_found']}
  🔧 Corrections Applied: {report['accuracy_metrics']['corrections_applied']}

QUALITY ASSESSMENT:
  ⭐ Overall Score: {report['quality_assessment']['overall_score']:.1f}%
  📈 Quality Level: {report['quality_assessment']['quality_level']}
  🤖 AI Used: {'Yes' if report['ai_usage'] else 'No'}
"""

def main():
    """Enhanced main function with comprehensive validation"""
    parser = argparse.ArgumentParser(description="Enhanced PDF to Excel Converter with AI Validation")
    parser.add_argument("pdf_path", help="Path to PDF file or directory")
    parser.add_argument("--output", "-o", help="Output directory", default=None)
    parser.add_argument("--model", "-m", help="Ollama model to use", default="llama3.1:8b")
    parser.add_argument("--no-auto-correct", action="store_true", help="Disable auto-correction")
    parser.add_argument("--batch", "-b", action="store_true", help="Batch process directory")
    parser.add_argument("--quality-report", "-q", type=int, help="Generate quality report for N days", default=0)
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize enhanced converter
    converter = EnhancedPDFConverter(
        model=args.model,
        auto_correct=not args.no_auto_correct
    )
    
    # Generate quality report if requested
    if args.quality_report > 0:
        print(converter.generate_quality_report(args.quality_report))
        return
    
    # Check if AI is available
    if not converter.ai_available:
        print("⚠️ Warning: AI validation not available. Install Ollama and run:")
        print("   ollama pull llama3.1:8b")
        print("   Continuing with pattern-based validation only...")
    
    pdf_path = Path(args.pdf_path)
    
    if not pdf_path.exists():
        print(f"❌ Error: Path not found: {args.pdf_path}")
        return
    
    try:
        if args.batch or pdf_path.is_dir():
            # Batch processing
            output_dir = args.output or str(pdf_path.parent / "converted_batch")
            print(f"🔄 Batch processing: {pdf_path} → {output_dir}")
            
            results = converter.batch_convert(str(pdf_path), output_dir)
            
            if results.get('error'):
                print(f"❌ Batch conversion failed: {results['error']}")
                return
            
            # Print batch summary
            print(f"\n✅ Batch Conversion Summary:")
            print(f"   📊 Total Files: {results['total_files']}")
            print(f"   ✅ Successful: {results['successful']}")
            print(f"   ❌ Failed: {results['failed']}")
            print(f"   📈 Success Rate: {results['success_rate']:.1f}%")
            
            # Show details for failed files
            failed_files = [r for r in results['results'] if not r.get('success')]
            if failed_files:
                print(f"\n❌ Failed Files:")
                for failed in failed_files:
                    print(f"   • {failed['filename']}: {failed.get('error', 'Unknown error')}")
        
        else:
            # Single file processing
            output_dir = args.output or str(pdf_path.parent)
            print(f"🔄 Converting: {pdf_path.name} → {output_dir}")
            
            results = converter.convert_with_validation(str(pdf_path), output_dir)
            
            if results.get('error'):
                print(f"❌ Conversion failed: {results['error']}")
                return
            
            # Print conversion summary
            summary = results.get('summary', {})
            accuracy = summary.get('accuracy', {})
            issues = summary.get('issues', {})
            improvements = summary.get('improvements', {})
            
            print(f"\n✅ Conversion Summary:")
            print(f"   🎯 Accuracy: {accuracy.get('match_rate', 0):.1f}% ({accuracy.get('quality_level', 'Unknown')})")
            print(f"   📊 Records: {accuracy.get('records_captured', 0)}/{accuracy.get('records_found', 0)} captured")
            print(f"   ⚠️ Issues: {issues.get('total', 0)} found ({issues.get('critical', 0)} critical)")
            print(f"   🔧 Improvements: {improvements.get('corrections_applied', 0)} corrections applied")
            print(f"   ⚡ Processing Time: {results.get('processing_time', 0):.2f}s")
            
            # Show file locations
            files = results.get('files', {})
            if files.get('validated_excel'):
                print(f"\n📁 Output Files:")
                print(f"   📄 Original: {files['original_excel']}")
                print(f"   ✨ Validated: {files['validated_excel']}")
            else:
                print(f"\n📁 Output File: {files.get('original_excel', 'Not created')}")
            
            # Show top recommendations
            recommendations = summary.get('recommendations', [])
            if recommendations:
                print(f"\n💡 Top Recommendations:")
                for i, rec in enumerate(recommendations[:3], 1):
                    print(f"   {i}. {rec.get('description', 'No description')}")
    
    except KeyboardInterrupt:
        print("\n🛑 Conversion cancelled by user")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        logger.exception("Conversion failed with exception")

if __name__ == "__main__":
    main()