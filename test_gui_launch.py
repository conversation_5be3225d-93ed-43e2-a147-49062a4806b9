#!/usr/bin/env python3
"""
Quick test to verify GUI launches without hanging
"""

import time
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_launch():
    """Test that GUI can be created without hanging"""
    print("Testing GUI launch...")
    
    start_time = time.time()
    
    try:
        from gui_simple import SimplifiedModernGUI
        import tkinter as tk
        
        # Create GUI (should be fast now)
        root = tk.Tk()
        gui = SimplifiedModernGUI()
        
        launch_time = time.time() - start_time
        print(f"[OK] GUI created successfully in {launch_time:.2f} seconds")
        
        # Test AI availability check (should be fast)
        ai_check_start = time.time()
        ai_available = gui._check_ai_availability()
        ai_check_time = time.time() - ai_check_start
        
        print(f"[OK] AI availability check completed in {ai_check_time:.2f} seconds")
        print(f"[INFO] AI Available: {ai_available}")
        
        # Test AI toggle
        if hasattr(gui, 'ai_validation_var'):
            print("[OK] AI validation toggle found")
            gui.ai_validation_var.set(True)
            gui._on_ai_toggle()
            print("[OK] AI toggle test passed")
        
        # Clean up
        root.destroy()
        
        total_time = time.time() - start_time
        print(f"[SUCCESS] All tests passed in {total_time:.2f} seconds total")
        
        if total_time < 5:
            print("[OK] GUI is responsive - no hanging issues")
        else:
            print("[WARNING] GUI took longer than expected")
            
        return True
        
    except Exception as e:
        print(f"[ERROR] Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_gui_launch()
    sys.exit(0 if success else 1)