#!/usr/bin/env python3
"""
Test to verify all GUI elements are visible and working
"""

import time
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_gui_elements():
    """Test that all GUI elements are visible and accessible"""
    print("Testing GUI element visibility...")
    
    try:
        from gui_simple import SimplifiedModernGUI
        import tkinter as tk
        
        # Create GUI
        root = tk.Tk()
        gui = SimplifiedModernGUI()
        
        # Test all checkbox variables exist
        checkboxes = [
            ('include_summary_var', 'Include summary sheet'),
            ('category_sheets_var', 'Create category sheets'), 
            ('auto_open_var', 'Auto-open Excel'),
            ('ai_validation_var', 'AI validation')
        ]
        
        print("\nChecking checkbox variables:")
        for var_name, description in checkboxes:
            if hasattr(gui, var_name):
                var = getattr(gui, var_name)
                value = var.get()
                print(f"[OK] {description}: {value}")
            else:
                print(f"[ERROR] Missing: {description}")
        
        # Test AI status label exists
        if hasattr(gui, 'ai_status_label'):
            print("[OK] AI status label found")
        else:
            print("[ERROR] AI status label missing")
        
        # Test conversion callback
        if hasattr(gui, 'conversion_callback'):
            print("[OK] Conversion callback attribute exists")
        else:
            print("[ERROR] Conversion callback missing")
        
        # Test AI toggle functionality
        print("\nTesting AI toggle:")
        original_value = gui.ai_validation_var.get()
        gui.ai_validation_var.set(not original_value)
        gui._on_ai_toggle()
        new_value = gui.ai_validation_var.get()
        print(f"[OK] AI toggle changed from {original_value} to {new_value}")
        
        # Clean up
        root.destroy()
        
        print("\n[SUCCESS] All GUI elements are accessible")
        return True
        
    except Exception as e:
        print(f"[ERROR] GUI test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gui_elements()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)