#!/usr/bin/env python3
"""
Excel Records Checker and Validator
Final validation and fixing of records before user access
"""

import pandas as pd
import logging
import os
import re
from typing import Dict, List, Tuple, Any
from config import EXCEL_COLUMNS

logger = logging.getLogger(__name__)

class ExcelRecordsChecker:
    """
    Comprehensive validator and fixer for Excel records
    """
    
    def __init__(self):
        self.validation_stats = {
            'total_records': 0,
            'main_records': 0,
            'broken_records': 0,
            'fixed_records': 0,
            'reclassified_records': 0,
            'format_violations': 0,
            'duplicate_records': 0
        }
        
        # Define what constitutes a broken record (stricter criteria)
        self.critical_fields = ['Location/Area', 'Size/Yards', 'Contact Person', 'Phone Numbers']
        self.important_fields = ['Property Code', 'Details']
        self.optional_fields = ['Property Type', 'Status']
    
    def validate_excel_file(self, excel_path: str) -> Dict[str, Any]:
        """
        Comprehensive validation of Excel file and its records
        
        Args:
            excel_path: Path to Excel file to validate
            
        Returns:
            dict: Validation results and recommendations
        """
        try:
            logger.info(f"🔍 STARTING EXCEL VALIDATION: {os.path.basename(excel_path)}")
            
            # Read all sheets
            excel_data = self._read_all_sheets(excel_path)
            
            if not excel_data:
                return {'status': 'error', 'message': 'Failed to read Excel file'}
            
            # Validate each sheet
            validation_results = {}
            
            for sheet_name, df in excel_data.items():
                if 'Property Data' in sheet_name or 'Broken' in sheet_name or 'Incomplete' in sheet_name:
                    logger.info(f"📋 Validating sheet: {sheet_name}")
                    sheet_results = self._validate_sheet(df, sheet_name)
                    validation_results[sheet_name] = sheet_results
            
            # Generate comprehensive report
            report = self._generate_validation_report(validation_results, excel_path)
            
            # Fix issues if any found
            if report['issues_found'] > 0:
                logger.warning(f"⚠️  Found {report['issues_found']} issues - applying fixes")
                fixed_path = self._apply_fixes(excel_path, validation_results)
                report['fixed_file'] = fixed_path
            
            return report
            
        except Exception as e:
            logger.error(f"❌ Excel validation failed: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def _read_all_sheets(self, excel_path: str) -> Dict[str, pd.DataFrame]:
        """Read all sheets from Excel file"""
        try:
            xl_file = pd.ExcelFile(excel_path)
            sheets = {}
            
            for sheet_name in xl_file.sheet_names:
                try:
                    df = pd.read_excel(excel_path, sheet_name=sheet_name)
                    sheets[sheet_name] = df
                    logger.debug(f"   📄 Read sheet '{sheet_name}': {len(df)} rows")
                except Exception as e:
                    logger.warning(f"   ⚠️  Could not read sheet '{sheet_name}': {str(e)}")
            
            return sheets
            
        except Exception as e:
            logger.error(f"❌ Failed to read Excel file: {str(e)}")
            return {}
    
    def _validate_sheet(self, df: pd.DataFrame, sheet_name: str) -> Dict[str, Any]:
        """Validate individual sheet"""
        results = {
            'total_records': len(df),
            'valid_records': 0,
            'broken_records': 0,
            'misclassified_records': [],
            'format_violations': [],
            'duplicate_records': [],
            'empty_critical_fields': [],
            'data_quality_issues': []
        }
        
        self.validation_stats['total_records'] += len(df)
        
        if 'All Property Data' in sheet_name:
            self.validation_stats['main_records'] += len(df)
        elif 'Broken' in sheet_name or 'Incomplete' in sheet_name:
            self.validation_stats['broken_records'] += len(df)
        
        # Validate each record
        for idx, row in df.iterrows():
            record_issues = self._validate_record(row, idx)
            
            if record_issues['is_misclassified']:
                results['misclassified_records'].append({
                    'index': idx,
                    'issues': record_issues,
                    'should_be': record_issues['correct_classification']
                })
            
            if record_issues['format_violations']:
                results['format_violations'].extend(record_issues['format_violations'])
            
            if record_issues['empty_critical_fields']:
                results['empty_critical_fields'].append({
                    'index': idx,
                    'missing_fields': record_issues['empty_critical_fields']
                })
            
            if record_issues['data_quality_issues']:
                results['data_quality_issues'].extend(record_issues['data_quality_issues'])
            
            if not record_issues['has_major_issues']:
                results['valid_records'] += 1
            else:
                results['broken_records'] += 1
        
        # Check for duplicates
        results['duplicate_records'] = self._find_duplicates(df)
        
        return results
    
    def _validate_record(self, row: pd.Series, index: int) -> Dict[str, Any]:
        """Validate individual record"""
        issues = {
            'is_misclassified': False,
            'correct_classification': None,
            'format_violations': [],
            'empty_critical_fields': [],
            'data_quality_issues': [],
            'has_major_issues': False
        }
        
        # Check for empty critical fields
        empty_critical = []
        for field in self.critical_fields:
            if field in row and (pd.isna(row[field]) or str(row[field]).strip() == '' or str(row[field]) == 'nan'):
                empty_critical.append(field)
        
        issues['empty_critical_fields'] = empty_critical
        
        # Check for empty important fields
        empty_important = []
        for field in self.important_fields:
            if field in row and (pd.isna(row[field]) or str(row[field]).strip() == '' or str(row[field]) == 'nan'):
                empty_important.append(field)
        
        # Determine if record should be classified as broken
        # More than 2 critical fields missing = definitely broken
        # More than 1 critical field + 1 important field missing = likely broken
        critical_missing = len(empty_critical)
        important_missing = len(empty_important)
        
        should_be_broken = (
            critical_missing > 2 or 
            (critical_missing >= 1 and important_missing >= 1 and critical_missing + important_missing > 2)
        )
        
        # Check if record is misclassified
        current_sheet_type = 'main'  # Assume main unless we detect broken markers
        if '_broken' in row and str(row.get('_broken', '')).lower() == 'true':
            current_sheet_type = 'broken'
        
        if should_be_broken and current_sheet_type == 'main':
            issues['is_misclassified'] = True
            issues['correct_classification'] = 'broken'
            issues['has_major_issues'] = True
        elif not should_be_broken and current_sheet_type == 'broken':
            issues['is_misclassified'] = True
            issues['correct_classification'] = 'main'
        
        # Check format violations
        if 'Property Code' in row and row['Property Code']:
            prop_code = str(row['Property Code']).strip()
            if len(prop_code) > 20:  # Unreasonably long property code
                issues['format_violations'].append({
                    'field': 'Property Code',
                    'issue': 'Too long',
                    'value': prop_code[:30] + '...',
                    'index': index
                })
        
        if 'Phone Numbers' in row and row['Phone Numbers']:
            phones = str(row['Phone Numbers'])
            # Check for invalid phone number formats
            phone_list = phones.split(',')
            for phone in phone_list:
                phone = phone.strip().replace('-', '').replace(' ', '')
                if phone and (len(phone) < 10 or len(phone) > 12 or not phone.isdigit()):
                    issues['format_violations'].append({
                        'field': 'Phone Numbers',
                        'issue': 'Invalid format',
                        'value': phone,
                        'index': index
                    })
        
        # Check data quality issues
        if 'Details' in row and row['Details']:
            details = str(row['Details'])
            if len(details) > 500:  # Extremely long details might indicate parsing error
                issues['data_quality_issues'].append({
                    'field': 'Details',
                    'issue': 'Extremely long content',
                    'value': details[:50] + '...',
                    'index': index
                })
        
        # Set major issues flag
        if (critical_missing > 1 or len(issues['format_violations']) > 2 or 
            len(issues['data_quality_issues']) > 1):
            issues['has_major_issues'] = True
        
        return issues
    
    def _find_duplicates(self, df: pd.DataFrame) -> List[Dict]:
        """Find potential duplicate records"""
        duplicates = []
        
        if len(df) < 2:
            return duplicates
        
        # Check for duplicates based on Property Code + Location
        if 'Property Code' in df.columns and 'Location/Area' in df.columns:
            df_clean = df.copy()
            df_clean['prop_code_clean'] = df_clean['Property Code'].astype(str).str.strip().str.upper()
            df_clean['location_clean'] = df_clean['Location/Area'].astype(str).str.strip().str.upper()
            
            # Find duplicates
            duplicate_mask = df_clean.duplicated(['prop_code_clean', 'location_clean'], keep=False)
            duplicate_indices = df_clean[duplicate_mask].index.tolist()
            
            if duplicate_indices:
                for idx in duplicate_indices:
                    duplicates.append({
                        'index': idx,
                        'property_code': df.loc[idx, 'Property Code'],
                        'location': df.loc[idx, 'Location/Area']
                    })
        
        return duplicates
    
    def _generate_validation_report(self, validation_results: Dict, excel_path: str) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        total_issues = 0
        report = {
            'file_path': excel_path,
            'status': 'success',
            'validation_stats': self.validation_stats.copy(),
            'sheet_details': {},
            'critical_issues': [],
            'recommendations': [],
            'issues_found': 0
        }
        
        for sheet_name, results in validation_results.items():
            total_issues += len(results['misclassified_records'])
            total_issues += len(results['format_violations'])
            total_issues += len(results['duplicate_records'])
            total_issues += len(results['empty_critical_fields'])
            
            report['sheet_details'][sheet_name] = {
                'total_records': results['total_records'],
                'valid_records': results['valid_records'],
                'broken_records': results['broken_records'],
                'misclassified': len(results['misclassified_records']),
                'format_violations': len(results['format_violations']),
                'duplicates': len(results['duplicate_records'])
            }
            
            # Generate critical issues
            if results['misclassified_records']:
                report['critical_issues'].append({
                    'sheet': sheet_name,
                    'type': 'misclassification',
                    'count': len(results['misclassified_records']),
                    'description': f"{len(results['misclassified_records'])} records are misclassified"
                })
            
            if results['empty_critical_fields']:
                report['critical_issues'].append({
                    'sheet': sheet_name,
                    'type': 'missing_critical_data',
                    'count': len(results['empty_critical_fields']),
                    'description': f"{len(results['empty_critical_fields'])} records missing critical fields"
                })
        
        report['issues_found'] = total_issues
        
        # Generate recommendations
        if total_issues > 0:
            report['recommendations'].append("🔧 Apply automatic fixes to resolve classification and format issues")
        
        if self.validation_stats['broken_records'] > self.validation_stats['main_records'] * 0.3:
            report['recommendations'].append("⚠️ High broken record ratio - consider improving extraction patterns")
        
        if any(len(results['duplicate_records']) > 0 for results in validation_results.values()):
            report['recommendations'].append("🗑️ Remove duplicate records to improve data quality")
        
        return report
    
    def _apply_fixes(self, excel_path: str, validation_results: Dict) -> str:
        """Apply fixes to Excel file"""
        try:
            # Create backup
            backup_path = excel_path.replace('.xlsx', '_backup.xlsx')
            if os.path.exists(excel_path):
                import shutil
                shutil.copy2(excel_path, backup_path)
                logger.info(f"📋 Created backup: {os.path.basename(backup_path)}")
            
            # Read and fix each sheet
            fixed_sheets = {}
            
            for sheet_name, results in validation_results.items():
                df = pd.read_excel(excel_path, sheet_name=sheet_name)
                
                # Apply fixes
                fixed_df = self._fix_sheet_issues(df, results, sheet_name)
                fixed_sheets[sheet_name] = fixed_df
            
            # Write fixed Excel file
            fixed_path = excel_path.replace('.xlsx', '_fixed.xlsx')
            
            try:
                with pd.ExcelWriter(fixed_path, engine='openpyxl') as writer:
                    for sheet_name, df in fixed_sheets.items():
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                logger.info(f"✅ Fixed Excel file created: {os.path.basename(fixed_path)}")
                return fixed_path
                
            except Exception as write_error:
                logger.error(f"❌ Failed to write fixed Excel file: {str(write_error)}")
                # Try alternative method - write to CSV and then convert
                try:
                    csv_path = excel_path.replace('.xlsx', '_fixed.csv')
                    # Write main sheet as CSV
                    main_sheet = None
                    for sheet_name, df in fixed_sheets.items():
                        if 'All Property Data' in sheet_name or 'main' in sheet_name.lower():
                            main_sheet = df
                            break
                    
                    if main_sheet is not None:
                        main_sheet.to_csv(csv_path, index=False)
                        logger.info(f"✅ Fixed data saved as CSV: {os.path.basename(csv_path)}")
                        return csv_path
                    
                except Exception as csv_error:
                    logger.error(f"❌ Failed to save as CSV: {str(csv_error)}")
                
                # Return original file if all else fails
                return excel_path
            
        except Exception as e:
            logger.error(f"❌ Failed to apply fixes: {str(e)}")
            return excel_path
    
    def _fix_sheet_issues(self, df: pd.DataFrame, results: Dict, sheet_name: str) -> pd.DataFrame:
        """Fix issues in individual sheet"""
        fixed_df = df.copy()
        
        # Fix misclassified records
        for misclassified in results['misclassified_records']:
            idx = misclassified['index']
            if misclassified['should_be'] == 'broken':
                # Mark as broken
                if '_broken' not in fixed_df.columns:
                    fixed_df['_broken'] = 'false'
                fixed_df.loc[idx, '_broken'] = 'true'
                self.validation_stats['reclassified_records'] += 1
            elif misclassified['should_be'] == 'main':
                # Remove broken flag
                if '_broken' in fixed_df.columns:
                    fixed_df.loc[idx, '_broken'] = 'false'
                self.validation_stats['reclassified_records'] += 1
        
        # Fix format violations
        for violation in results['format_violations']:
            idx = violation['index']
            field = violation['field']
            
            if field == 'Property Code' and violation['issue'] == 'Too long':
                # Truncate property code
                original = str(fixed_df.loc[idx, field])
                fixed_df.loc[idx, field] = original[:15] + '...'
                self.validation_stats['fixed_records'] += 1
            
            elif field == 'Phone Numbers' and violation['issue'] == 'Invalid format':
                # Try to clean phone number
                phones = str(fixed_df.loc[idx, field])
                cleaned_phones = []
                for phone in phones.split(','):
                    phone = phone.strip()
                    # Extract only digits
                    digits_only = re.sub(r'[^\d]', '', phone)
                    if len(digits_only) >= 10:
                        cleaned_phones.append(digits_only[:10])
                
                if cleaned_phones:
                    fixed_df.loc[idx, field] = ', '.join(cleaned_phones)
                    self.validation_stats['fixed_records'] += 1
        
        # Remove exact duplicates
        if results['duplicate_records']:
            before_count = len(fixed_df)
            fixed_df = fixed_df.drop_duplicates(subset=['Property Code', 'Location/Area'], keep='first')
            after_count = len(fixed_df)
            removed = before_count - after_count
            if removed > 0:
                self.validation_stats['duplicate_records'] += removed
                logger.info(f"   🗑️ Removed {removed} duplicate records from {sheet_name}")
        
        return fixed_df
    
    def print_validation_report(self, report: Dict):
        """Print formatted validation report"""
        print("\n" + "="*80)
        print("🔍 EXCEL RECORDS VALIDATION REPORT")
        print("="*80)
        
        print(f"\n📁 File: {os.path.basename(report['file_path'])}")
        print(f"🎯 Status: {report['status'].upper()}")
        print(f"⚠️ Issues Found: {report['issues_found']}")
        
        print(f"\n📊 OVERALL STATISTICS:")
        stats = report['validation_stats']
        print(f"   Total Records: {stats['total_records']}")
        print(f"   Main Records: {stats['main_records']}")
        print(f"   Broken Records: {stats['broken_records']}")
        if stats['fixed_records'] > 0:
            print(f"   Fixed Records: {stats['fixed_records']}")
        if stats['reclassified_records'] > 0:
            print(f"   Reclassified Records: {stats['reclassified_records']}")
        if stats['duplicate_records'] > 0:
            print(f"   Duplicates Removed: {stats['duplicate_records']}")
        
        print(f"\n📋 SHEET BREAKDOWN:")
        for sheet_name, details in report['sheet_details'].items():
            print(f"   {sheet_name}:")
            print(f"      Total: {details['total_records']}")
            print(f"      Valid: {details['valid_records']}")
            if details['misclassified'] > 0:
                print(f"      Misclassified: {details['misclassified']}")
            if details['format_violations'] > 0:
                print(f"      Format Issues: {details['format_violations']}")
            if details['duplicates'] > 0:
                print(f"      Duplicates: {details['duplicates']}")
        
        if report['critical_issues']:
            print(f"\n🚨 CRITICAL ISSUES:")
            for issue in report['critical_issues']:
                print(f"   {issue['sheet']}: {issue['description']}")
        
        if report['recommendations']:
            print(f"\n💡 RECOMMENDATIONS:")
            for rec in report['recommendations']:
                print(f"   {rec}")
        
        if 'fixed_file' in report:
            print(f"\n✅ FIXED FILE CREATED: {os.path.basename(report['fixed_file'])}")
        
        print("="*80)

# Example usage and testing
if __name__ == "__main__":
    checker = ExcelRecordsChecker()
    
    # Test with existing Excel file
    excel_file = "Real_Estate_Data_20250725.xlsx"
    if os.path.exists(excel_file):
        report = checker.validate_excel_file(excel_file)
        checker.print_validation_report(report)
    else:
        print("❌ Excel file not found for testing") 