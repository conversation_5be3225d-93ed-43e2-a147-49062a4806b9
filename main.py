"""
Main Application - PDF to Excel Converter
Integrates all modules to provide complete conversion functionality
"""

import os
import sys
import logging
import tkinter as tk
from typing import List, Callable, Optional, Dict
import traceback

# Import our modules
from pdf_parser import PDFParser
from data_processor import DataProcessor
from excel_generator import ExcelG<PERSON>ator
from excel_records_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from gui_simple import SimplifiedModernG<PERSON>
from gui_modern import ModernPDFConverterGUI
from config import PROCESSING_CONFIG

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_converter.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class PDFToExcelConverter:
    """
    Main application class that orchestrates the entire conversion process
    """
    
    def __init__(self):
        self.pdf_parser = PDFParser()
        self.data_processor = DataProcessor()
        self.excel_generator = ExcelGenerator()
        self.excel_checker = ExcelRecordsChecker()
        self.gui = None
        
        logger.info("PDF to Excel Converter with validation initialized")
    
    def convert_pdfs_to_excel(self, 
                              pdf_files: List[str], 
                              output_path: str,
                              progress_callback: Optional[Callable] = None,
                              status_callback: Optional[Callable] = None,
                              conversion_settings: Optional[Dict] = None) -> bool:
        """
        Convert multiple PDF files to a single Excel file
        
        Args:
            pdf_files: List of PDF file paths
            output_path: Output Excel file path
            progress_callback: Optional progress update callback
            status_callback: Optional status message callback
            
        Returns:
            bool: True if conversion successful, False otherwise
        """
        try:
            # Validate inputs
            if not pdf_files:
                error_msg = "No PDF files provided for conversion"
                if status_callback:
                    status_callback(error_msg, "error")
                logger.error(error_msg)
                return False
            
            if not output_path:
                error_msg = "No output path provided"
                if status_callback:
                    status_callback(error_msg, "error")
                logger.error(error_msg)
                return False
            
            # Validate conversion settings
            if conversion_settings is None:
                conversion_settings = {
                    'include_summary': True,
                    'create_category_sheets': True,
                    'include_incomplete_records': True,
                    'auto_open_result': False
                }
            
            total_steps = len(pdf_files) + 3  # PDF parsing + processing + Excel creation + finalization
            current_step = 0
            
            def update_progress(message: str):
                nonlocal current_step
                current_step += 1
                if progress_callback:
                    progress_callback(current_step, total_steps, message)
                if status_callback:
                    status_callback(message)
                logger.info(message)
            
            update_progress("Starting conversion process...")
            
            # Log conversion settings
            if status_callback:
                status_callback(f"Conversion settings: Summary sheet: {conversion_settings.get('include_summary', True)}, " +
                              f"Category sheets: {conversion_settings.get('create_category_sheets', True)}, " +
                              f"Auto-open: {conversion_settings.get('auto_open_result', False)}", "info")
            
            # Step 1: Extract text from all PDF files
            update_progress("Extracting text from PDF files...")
            
            def pdf_progress_callback(current, total, filename):
                if status_callback:
                    status_callback(f"Processing PDF {current}/{total}: {filename}")
            
            extracted_texts = self.pdf_parser.extract_from_multiple_pdfs(
                pdf_files, 
                pdf_progress_callback
            )
            
            if not extracted_texts:
                error_msg = "No text could be extracted from any PDF files"
                if status_callback:
                    status_callback(error_msg, "error")
                logger.error(error_msg)
                return False
            
            update_progress(f"Successfully extracted text from {len(extracted_texts)} PDF files")
            
            # Step 2: Process extracted text into structured data
            update_progress("Processing and structuring data...")
            
            # Enable debug mode for more lenient record capture (only reject records with >6 missing fields)
            self.data_processor.set_debug_mode(True)
            
            structured_data = self.data_processor.process_multiple_files(extracted_texts)
            
            if structured_data.empty:
                error_msg = "No structured data could be extracted from the PDF files"
                if status_callback:
                    status_callback(error_msg, "error")
                logger.error(error_msg)
                return False
            
            update_progress(f"Successfully processed {len(structured_data)} records")
            
            # Step 3: Generate Excel file
            update_progress("Creating Excel file with formatting...")
            
            # Extract settings for Excel generation
            include_summary = conversion_settings.get('include_summary', True)
            create_category_sheets = conversion_settings.get('create_category_sheets', True)
            include_incomplete_records = conversion_settings.get('include_incomplete_records', True)
            
            excel_file_path = self.excel_generator.create_excel_file(
                structured_data, 
                output_path,
                include_summary=include_summary,
                create_category_sheets=create_category_sheets,
                include_incomplete_records=include_incomplete_records
            )
            
            if not os.path.exists(excel_file_path):
                error_msg = "Excel file creation failed"
                if status_callback:
                    status_callback(error_msg, "error")
                logger.error(error_msg)
                return False
            
            # Step 4: Excel Validation - TEMPORARILY DISABLED
            # TODO: Re-enable after fixing location detection issues
            if status_callback:
                status_callback("⚠️ Excel validation temporarily disabled for debugging", "warning")
            
            # Step 5: Finalization and summary
            update_progress("Finalizing conversion...")
            
            # Get processing statistics
            parser_stats = self.pdf_parser.get_processing_stats()
            processor_summary = self.data_processor.get_processing_summary()
            
            # Create summary message
            summary_msg = self._create_conversion_summary(
                excel_file_path, 
                parser_stats, 
                processor_summary, 
                len(structured_data)
            )
            
            if status_callback:
                status_callback(summary_msg, "success")
            
            logger.info("Conversion completed successfully")
            logger.info(summary_msg)
            
            # Generate debug report if there are issues with record count
            debug_summary = self.data_processor.get_debug_summary()
            total_captured = debug_summary['debug_stats']['main_records_created']
            if debug_summary['debug_stats']['broken_records_created'] > 5:  # If more than 5 records created as broken
                logger.warning("🔍 GENERATING DEBUG REPORT due to potential record loss...")
                self.data_processor.print_debug_report()
            
            return True
            
        except Exception as e:
            error_msg = f"Conversion failed: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            
            if status_callback:
                status_callback(error_msg, "error")
            
            return False
    
    def _create_conversion_summary(self, 
                                   excel_path: str, 
                                   parser_stats: dict, 
                                   processor_summary: dict, 
                                   total_records: int) -> str:
        """
        Create a comprehensive conversion summary
        
        Args:
            excel_path: Path to created Excel file
            parser_stats: PDF parser statistics
            processor_summary: Data processor summary
            total_records: Total number of records converted
            
        Returns:
            str: Formatted summary message
        """
        file_size_mb = os.path.getsize(excel_path) / (1024 * 1024)
        
        summary = f"""
CONVERSION COMPLETED SUCCESSFULLY! 🎉

📊 Results Summary:
• Excel file created: {os.path.basename(excel_path)}
• File size: {file_size_mb:.2f} MB
• Total records extracted: {total_records}
• PDF files processed: {parser_stats.get('successful', 0)}/{parser_stats.get('total', 0)}
• Processing errors: {processor_summary.get('errors', 0)}

📁 Output location: {excel_path}

✨ Features included:
• Summary sheet with statistics
• Main data sheet with all records
• Separate sheets by property category
• Professional formatting and styling
• Auto-sized columns for readability

Ready to open in Excel! 
        """.strip()
        
        return summary
    
    def run_gui_mode(self):
        """
        Run the application in GUI mode
        """
        try:
            logger.info("Starting GUI mode")
            
            # Create GUI with AI validation support
            self.gui = SimplifiedModernGUI()
            
            # Set conversion callback
            self.gui.set_conversion_callback(self.convert_pdfs_to_excel)
            
            # Add welcome message
            welcome_msg = """Welcome to PDF to Excel Converter! 

This tool converts real estate PDF listings into structured Excel format with:
• Automatic data extraction and parsing
• Beautiful Excel formatting with multiple sheets
• Summary statistics and validation
• Support for batch processing

To get started:
1. Select your PDF files or choose a folder
2. Choose output location (optional)
3. Click 'Convert to Excel'

The application will process all files and create a comprehensive Excel workbook."""
            
            self.gui.add_status_message(welcome_msg, "info")
            
            # Run the GUI
            self.gui.run()
            
        except Exception as e:
            logger.error(f"GUI mode failed: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    
    def run_command_line_mode(self, pdf_path: str, output_path: Optional[str] = None):
        """
        Run the application in command line mode
        
        Args:
            pdf_path: Path to PDF file or directory containing PDFs
            output_path: Optional output Excel file path
        """
        try:
            logger.info("Starting command line mode")
            
            # Determine PDF files to process
            if os.path.isfile(pdf_path):
                if pdf_path.lower().endswith('.pdf'):
                    pdf_files = [pdf_path]
                else:
                    logger.error(f"File is not a PDF: {pdf_path}")
                    return False
            elif os.path.isdir(pdf_path):
                import glob
                pdf_files = glob.glob(os.path.join(pdf_path, "*.pdf"))
                if not pdf_files:
                    logger.error(f"No PDF files found in directory: {pdf_path}")
                    return False
            else:
                logger.error(f"Path does not exist: {pdf_path}")
                return False
            
            # Set default output path if not provided
            if output_path is None:
                if os.path.isfile(pdf_path):
                    base_dir = os.path.dirname(pdf_path)
                else:
                    base_dir = pdf_path
                output_path = os.path.join(base_dir, PROCESSING_CONFIG['output_filename'])
            
            # Progress callback for command line
            def progress_callback(current, total, message):
                print(f"Progress: {current}/{total} - {message}")
            
            # Status callback for command line
            def status_callback(message, msg_type="info"):
                prefix = "ERROR" if msg_type == "error" else "SUCCESS" if msg_type == "success" else "INFO"
                print(f"[{prefix}] {message}")
            
            # Run conversion with default settings for command line
            default_settings = {
                'include_summary': True,
                'create_category_sheets': True,
                'include_incomplete_records': True,
                'auto_open_result': False
            }
            
            success = self.convert_pdfs_to_excel(
                pdf_files, 
                output_path, 
                progress_callback, 
                status_callback,
                default_settings
            )
            
            return success
            
        except Exception as e:
            logger.error(f"Command line mode failed: {str(e)}")
            logger.error(traceback.format_exc())
            return False

def main():
    """
    Main entry point for the application
    """
    try:
        converter = PDFToExcelConverter()
        
        # Check command line arguments
        if len(sys.argv) > 1:
            # Command line mode
            pdf_path = sys.argv[1]
            output_path = sys.argv[2] if len(sys.argv) > 2 else None
            
            print("PDF to Excel Converter - Command Line Mode")
            print("=" * 50)
            print(f"Input: {pdf_path}")
            print(f"Output: {output_path or 'Auto-generated'}")
            print()
            
            success = converter.run_command_line_mode(pdf_path, output_path)
            
            if success:
                print("\n✅ Conversion completed successfully!")
            else:
                print("\n❌ Conversion failed!")
                sys.exit(1)
        else:
            # GUI mode
            converter.run_gui_mode()
            
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        print("\nApplication interrupted by user")
    except Exception as e:
        logger.error(f"Application failed: {str(e)}")
        logger.error(traceback.format_exc())
        print(f"\n❌ Application failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 