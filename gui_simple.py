"""
Simplified Modern GUI Module - Clean and functional PDF to Excel Converter Interface
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import os
import glob
import datetime
import json
import requests
from typing import List, Callable, Optional, Dict, Any
import logging
from tkinterdnd2 import DND_FILES, TkinterDnD  # type: ignore

# Configure logging
logger = logging.getLogger(__name__)

class SimplifiedModernGUI:
    """
    Simplified modern GUI with clean layout and enhanced functionality
    """
    
    def __init__(self):
        self.root = TkinterDnD.Tk()
        self.setup_window()
        self.create_styles()
        self.settings = self.load_settings()  # Load settings BEFORE creating widgets
        self.selected_files = []
        self.conversion_callback: Optional[Callable] = None
        self.create_widgets()
        
    def setup_window(self):
        """Setup main window with modern styling"""
        self.root.title("PDF to Excel Converter - Modern Edition")
        self.root.geometry("1000x800")
        self.root.minsize(900, 700)
        self.root.configure(bg='#f5f5f5')
        
        # Center the window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_reqwidth() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_reqheight() // 2)
        self.root.geometry(f"+{x}+{y}")
    
    def create_styles(self):
        """Create modern color scheme and styles"""
        self.colors = {
            'primary': '#2563eb',
            'primary_hover': '#1d4ed8', 
            'secondary': '#64748b',
            'success': '#10b981',
            'warning': '#f59e0b',
            'danger': '#ef4444',
            'light': '#f8fafc',
            'dark': '#1e293b',
            'white': '#ffffff',
            'border': '#e2e8f0'
        }
    
    def load_settings(self) -> Dict[str, Any]:
        """Load user settings"""
        return {
            'output_directory': os.getcwd(),
            'filename_pattern': 'Real_Estate_Data_{date}',
            'output_format': 'xlsx',
            'include_summary': True,
            'create_category_sheets': True,
            'auto_open_result': True,
            'ai_validation_enabled': True,  # Enable AI by default
            'ai_model': 'llama3.1:8b',
            'ai_auto_correct': True
        }
    
    def create_widgets(self):
        """Create all GUI widgets using consistent pack layout"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header
        self.create_header(main_frame)
        
        # Content area
        content_frame = tk.Frame(main_frame, bg='#f5f5f5')
        content_frame.pack(fill='both', expand=True, pady=(20, 0))
        
        # Left section - File Selection
        left_frame = tk.Frame(content_frame, bg='white', relief='solid', bd=1, width=300)
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        left_frame.pack_propagate(False)
        
        self.create_file_section(left_frame)
        
        # Right section - Settings and Processing
        right_frame = tk.Frame(content_frame, bg='white', relief='solid', bd=1, width=300)
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        right_frame.pack_propagate(False)
        
        self.create_settings_section(right_frame)
        
        # Bottom section - Status
        self.create_status_section(main_frame)
    
    def create_header(self, parent):
        """Create header section"""
        header_frame = tk.Frame(parent, bg='white', relief='solid', bd=1, height=80)
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(header_frame,
                              text="PDF to Excel Converter",
                              font=('Segoe UI', 20, 'bold'),
                              fg=self.colors['dark'],
                              bg='white')
        title_label.pack(pady=(15, 5))
        
        # Subtitle
        subtitle_label = tk.Label(header_frame,
                                 text="Transform real estate PDF listings into structured Excel spreadsheets",
                                 font=('Segoe UI', 11),
                                 fg=self.colors['secondary'],
                                 bg='white')
        subtitle_label.pack()
    
    def create_file_section(self, parent):
        """Create file selection section"""
        # Header
        header_frame = tk.Frame(parent, bg=self.colors['light'], height=40)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="📁 Select PDF Files",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['light'], fg=self.colors['dark']).pack(pady=10)
        
        # Content
        content_frame = tk.Frame(parent, bg='white')
        content_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        # Drag and drop area
        self.drop_frame = tk.Frame(content_frame, bg='#f8fafc', relief='ridge', bd=2, height=120)
        self.drop_frame.pack(fill='x', pady=(0, 15))
        self.drop_frame.pack_propagate(False)
        
        self.drop_icon = tk.Label(self.drop_frame, text="📄",
                font=('Segoe UI', 32),
                bg='#f8fafc', fg=self.colors['secondary'])
        self.drop_icon.pack(pady=(15, 5))
        
        self.drop_label = tk.Label(self.drop_frame, text="Drag & Drop PDF files here",
                font=('Segoe UI', 10),
                bg='#f8fafc', fg=self.colors['secondary'])
        self.drop_label.pack()
        
        # Configure drag and drop
        self.setup_drag_drop()
        
        # Buttons
        button_frame = tk.Frame(content_frame, bg='white')
        button_frame.pack(fill='x', pady=(0, 15))
        
        select_files_btn = tk.Button(button_frame,
                                   text="Browse Files",
                                   command=self.select_files,
                                   bg=self.colors['primary'],
                                   fg='white',
                                   font=('Segoe UI', 10, 'bold'),
                                   relief='flat',
                                   padx=20, pady=8,
                                   cursor='hand2')
        select_files_btn.pack(side='left', padx=(0, 10))
        
        select_folder_btn = tk.Button(button_frame,
                                    text="Browse Folder", 
                                    command=self.select_folder,
                                    bg=self.colors['secondary'],
                                    fg='white',
                                    font=('Segoe UI', 10),
                                    relief='flat',
                                    padx=20, pady=8,
                                    cursor='hand2')
        select_folder_btn.pack(side='left')
        
        # File list
        list_frame = tk.Frame(content_frame, bg='white')
        list_frame.pack(fill='both', expand=True)
        
        # List header
        list_header = tk.Frame(list_frame, bg=self.colors['light'])
        list_header.pack(fill='x')
        
        tk.Label(list_header, text="Selected Files",
                font=('Segoe UI', 9, 'bold'),
                bg=self.colors['light'], fg=self.colors['dark']).pack(side='left', padx=10, pady=5)
        
        clear_btn = tk.Button(list_header, text="Clear",
                            command=self.clear_files,
                            bg=self.colors['danger'], fg='white',
                            font=('Segoe UI', 8), relief='flat',
                            padx=8, pady=2, cursor='hand2')
        clear_btn.pack(side='right', padx=5, pady=3)
        
        # Listbox
        self.file_listbox = tk.Listbox(list_frame,
                                      font=('Segoe UI', 9),
                                      relief='flat',
                                      selectbackground=self.colors['primary'],
                                      bg='white', fg=self.colors['dark'],
                                      borderwidth=0)
        self.file_listbox.pack(fill='both', expand=True)
    
    def create_settings_section(self, parent):
        """Create settings and processing section"""
        # Header
        header_frame = tk.Frame(parent, bg=self.colors['light'], height=40)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="⚙️ Settings & Processing",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['light'], fg=self.colors['dark']).pack(pady=10)
        
        # Action Buttons (positioned at top for visibility)
        action_frame = tk.Frame(parent, bg='white')
        action_frame.pack(fill='x', padx=15, pady=(10, 0))
        self.create_action_buttons(action_frame)
        
        # Content
        content_frame = tk.Frame(parent, bg='white')
        content_frame.pack(fill='both', expand=True, padx=15, pady=15)
        
        # Output Settings
        self.create_output_settings(content_frame)
        
        # Processing Options
        self.create_processing_options(content_frame)
        
        # Progress Section
        self.create_progress_section(content_frame)
    
    def create_output_settings(self, parent):
        """Create output settings"""
        settings_frame = tk.LabelFrame(parent, text="Output Settings", 
                                     font=('Segoe UI', 10, 'bold'),
                                     bg='white', fg=self.colors['dark'],
                                     padx=10, pady=8)
        settings_frame.pack(fill='x', pady=(0, 10))
        
        # Output Directory
        tk.Label(settings_frame, text="Directory:",
                font=('Segoe UI', 9, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w')
        
        dir_frame = tk.Frame(settings_frame, bg='white')
        dir_frame.pack(fill='x', pady=(5, 10))
        
        self.output_var = tk.StringVar(value=self.settings['output_directory'])
        output_entry = tk.Entry(dir_frame, textvariable=self.output_var,
                              font=('Segoe UI', 9), relief='solid', bd=1)
        output_entry.pack(side='left', fill='x', expand=True, padx=(0, 5))
        
        browse_btn = tk.Button(dir_frame, text="Browse",
                             command=self.browse_output,
                             bg=self.colors['secondary'], fg='white',
                             font=('Segoe UI', 9), relief='flat',
                             padx=10, pady=3, cursor='hand2')
        browse_btn.pack(side='right')
        
        # Filename Pattern
        tk.Label(settings_frame, text="Filename:",
                font=('Segoe UI', 9, 'bold'),
                bg='white', fg=self.colors['dark']).pack(anchor='w')
        
        self.filename_var = tk.StringVar(value=self.settings['filename_pattern'])
        filename_entry = tk.Entry(settings_frame, textvariable=self.filename_var,
                                font=('Segoe UI', 9), relief='solid', bd=1)
        filename_entry.pack(fill='x', pady=(5, 5))
        
        tk.Label(settings_frame, text="Use {date}, {time} for dynamic naming",
                font=('Segoe UI', 8), fg=self.colors['secondary'],
                bg='white').pack(anchor='w')
    
    def create_processing_options(self, parent):
        """Create processing options - just the button without frame"""
        # Initialize option variables
        self.include_summary_var = tk.BooleanVar(value=self.settings['include_summary'])
        self.category_sheets_var = tk.BooleanVar(value=self.settings['create_category_sheets'])
        self.auto_open_var = tk.BooleanVar(value=self.settings['auto_open_result'])
        self.ai_validation_var = tk.BooleanVar(value=self.settings.get('ai_validation_enabled', False))
        
        # Current settings display (compact)
        self.settings_display = tk.Label(parent,
                                       text="Processing: Default settings",
                                       font=('Segoe UI', 9), bg='white',
                                       fg=self.colors['secondary'])
        self.settings_display.pack(pady=(0, 8))
        
        # Button to open options dialog - standalone
        options_button = tk.Button(parent,
                                 text="🔧 Configure Processing Options",
                                 command=self.show_processing_options_dialog,
                                 bg=self.colors['primary'],
                                 fg='white',
                                 font=('Segoe UI', 11, 'bold'),
                                 relief='flat',
                                 padx=25, pady=12,
                                 cursor='hand2')
        options_button.pack(pady=(0, 15))
        
        # Update display initially
        self.root.after(100, self.update_settings_display)
    
    def create_progress_section(self, parent):
        """Create progress section"""
        progress_frame = tk.LabelFrame(parent, text="Progress",
                                     font=('Segoe UI', 10, 'bold'),
                                     bg='white', fg=self.colors['dark'],
                                     padx=10, pady=8)
        progress_frame.pack(fill='x', pady=(0, 10))
        
        # Progress label
        self.progress_label = tk.Label(progress_frame,
                                     text="Ready to convert",
                                     font=('Segoe UI', 9),
                                     bg='white', fg=self.colors['dark'])
        self.progress_label.pack(anchor='w')
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame,
                                          variable=self.progress_var,
                                          maximum=100)
        self.progress_bar.pack(fill='x', pady=(5, 0))
    
    def create_action_buttons(self, parent):
        """Create action buttons"""
        button_frame = tk.Frame(parent, bg='white', relief='solid', bd=1)
        button_frame.pack(fill='x', pady=(0, 15))
        
        # Button container
        btn_container = tk.Frame(button_frame, bg='white')
        btn_container.pack(pady=15)
        
        # Convert button
        self.convert_btn = tk.Button(btn_container,
                                   text="🚀 Convert to Excel",
                                   command=self.start_conversion,
                                   bg=self.colors['success'],
                                   fg='white',
                                   font=('Segoe UI', 12, 'bold'),
                                   relief='flat',
                                   padx=40, pady=12,
                                   cursor='hand2')
        self.convert_btn.pack(pady=(0, 10))
        
        # Cancel button
        self.cancel_btn = tk.Button(btn_container,
                                  text="❌ Cancel",
                                  command=self.cancel_conversion,
                                  bg=self.colors['danger'],
                                  fg='white',
                                  font=('Segoe UI', 10),
                                  relief='flat',
                                  padx=25, pady=8,
                                  cursor='hand2',
                                  state='disabled')
        self.cancel_btn.pack()
    
    def create_status_section(self, parent):
        """Create status section"""
        status_frame = tk.Frame(parent, bg='white', relief='solid', bd=1, height=150)
        status_frame.pack(fill='x', pady=(20, 0))
        status_frame.pack_propagate(False)
        
        # Header
        header_frame = tk.Frame(status_frame, bg=self.colors['light'], height=30)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="📋 Status Log",
                font=('Segoe UI', 10, 'bold'),
                bg=self.colors['light'], fg=self.colors['dark']).pack(side='left', padx=10, pady=8)
        
        # Text area
        text_frame = tk.Frame(status_frame, bg='white')
        text_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.status_text = tk.Text(text_frame,
                                 height=6,
                                 font=('Consolas', 9),
                                 bg='#f8fafc',
                                 fg=self.colors['dark'],
                                 relief='flat',
                                 wrap='word')
        
        scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Configure tags
        self.status_text.tag_configure("info", foreground=self.colors['dark'])
        self.status_text.tag_configure("success", foreground=self.colors['success'])
        self.status_text.tag_configure("warning", foreground=self.colors['warning'])
        self.status_text.tag_configure("error", foreground=self.colors['danger'])
        
        # Welcome message
        self.add_status_message("Welcome! Select PDF files to begin conversion.", "info")
    
    # Event handlers
    def select_files(self):
        """Select PDF files"""
        files = filedialog.askopenfilenames(
            title="Select PDF Files",
            filetypes=[('PDF files', '*.pdf'), ('All files', '*.*')],
            initialdir=self.settings['output_directory']
        )
        
        if files:
            self.selected_files.extend(files)
            self.update_file_list()
            self.add_status_message(f"Added {len(files)} PDF file(s)", "info")
    
    def select_folder(self):
        """Select folder with PDFs"""
        folder = filedialog.askdirectory(
            title="Select Folder with PDF Files",
            initialdir=self.settings['output_directory']
        )
        
        if folder:
            pdf_files = glob.glob(os.path.join(folder, "*.pdf"))
            if pdf_files:
                self.selected_files.extend(pdf_files)
                self.update_file_list()
                self.add_status_message(f"Found {len(pdf_files)} PDF file(s) in folder", "info")
            else:
                messagebox.showwarning("No PDF Files", "No PDF files found in the selected folder.")
    
    def clear_files(self):
        """Clear selected files"""
        self.selected_files.clear()
        self.update_file_list()
        self.add_status_message("File selection cleared", "info")
    
    def browse_output(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(
            title="Select Output Directory",
            initialdir=self.settings['output_directory']
        )
        
        if directory:
            self.output_var.set(directory)
            self.settings['output_directory'] = directory
    
    def update_file_list(self):
        """Update file listbox"""
        self.file_listbox.delete(0, tk.END)
        for file_path in self.selected_files:
            filename = os.path.basename(file_path)
            self.file_listbox.insert(tk.END, f"📄 {filename}")
    
    def add_status_message(self, message: str, message_type: str = "info"):
        """Add status message"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.status_text.insert(tk.END, f"[{timestamp}] {message}\n", message_type)
        self.status_text.see(tk.END)
        self.root.update_idletasks()
    
    def update_progress(self, current: int, total: int, message: str = ""):
        """Update progress"""
        if total > 0:
            progress_percent = (current / total) * 100
            self.progress_var.set(progress_percent)
        
        if message:
            self.progress_label.config(text=message)
        
        self.root.update_idletasks()
    
    def _check_ai_availability(self):
        """Check if AI validation is available - quick check only"""
        try:
            import requests
            # Quick ping to Ollama server (timeout 5 seconds)
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            
            if response.status_code == 200:
                # Check if our model exists
                models = response.json().get('models', [])
                for model in models:
                    if 'llama3.1:8b' in model.get('name', ''):
                        return True
                # Ollama is running but model not found
                return False
            else:
                return False
        except Exception as e:
            return False
    
    def _check_and_update_ai_status(self):
        """Check AI availability and update status label in background"""
        import threading
        
        def check_ai_in_background():
            try:
                ai_available = self._check_ai_availability()
                # Update UI from main thread
                self.root.after(0, lambda: self._update_ai_status_ui(ai_available))
            except Exception as e:
                self.root.after(0, lambda: self._update_ai_status_ui(False, str(e)))
        
        # Run check in background thread
        thread = threading.Thread(target=check_ai_in_background, daemon=True)
        thread.start()
    
    def _update_ai_status_ui(self, ai_available, error_msg=None):
        """Update AI status UI (called from main thread)"""
        try:
            if ai_available:
                self.ai_status_label.config(
                    text="AI Status: ✅ Available (Ollama running)",
                    fg=self.colors['success']
                )
            else:
                self.ai_status_label.config(
                    text="AI Status: ❌ Ollama not running",
                    fg=self.colors['danger']
                )
        except Exception:
            self.ai_status_label.config(
                text="AI Status: ❌ Connection failed",
                fg=self.colors['danger']
            )
    
    def update_settings_display(self):
        """Update the settings display text"""
        try:
            settings_text = []
            if self.include_summary_var.get():
                settings_text.append("+ Summary sheet")
            if self.category_sheets_var.get():
                settings_text.append("+ Category sheets")
            if self.auto_open_var.get():
                settings_text.append("+ Auto-open")
            if self.ai_validation_var.get():
                settings_text.append("+ AI validation")
            
            if settings_text:
                display_text = "Current: " + ", ".join(settings_text)
            else:
                display_text = "No options selected"
            
            self.settings_display.config(text=display_text)
        except:
            pass
    
    def show_processing_options_dialog(self):
        """Show processing options in a separate dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Processing Options")
        dialog.geometry("500x400")
        dialog.configure(bg='white')
        dialog.resizable(False, False)
        
        # Center the dialog
        dialog.transient(self.root)
        dialog.grab_set()
        
        # Main frame
        main_frame = tk.Frame(dialog, bg='white', padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # Title
        title_label = tk.Label(main_frame,
                             text="Configure Processing Options",
                             font=('Segoe UI', 14, 'bold'),
                             bg='white', fg=self.colors['dark'])
        title_label.pack(pady=(0, 20))
        
        # Options section
        options_section = tk.Frame(main_frame, bg='white')
        options_section.pack(fill='x', pady=(0, 20))
        
        # Excel Output Options
        excel_frame = tk.LabelFrame(options_section, text="Excel Output Options",
                                  font=('Segoe UI', 10, 'bold'),
                                  bg='white', fg=self.colors['dark'],
                                  padx=15, pady=15)
        excel_frame.pack(fill='x', pady=(0, 15))
        
        summary_check = tk.Checkbutton(excel_frame, text="Include summary sheet with statistics",
                                     variable=self.include_summary_var,
                                     font=('Segoe UI', 10), bg='white',
                                     fg=self.colors['dark'])
        summary_check.pack(anchor='w', pady=5)
        
        category_check = tk.Checkbutton(excel_frame, text="Create separate sheets by property category",
                                      variable=self.category_sheets_var,
                                      font=('Segoe UI', 10), bg='white',
                                      fg=self.colors['dark'])
        category_check.pack(anchor='w', pady=5)
        
        auto_open_check = tk.Checkbutton(excel_frame, text="Automatically open Excel file when done",
                                       variable=self.auto_open_var,
                                       font=('Segoe UI', 10), bg='white',
                                       fg=self.colors['dark'])
        auto_open_check.pack(anchor='w', pady=5)
        
        # AI Validation Section
        ai_frame = tk.LabelFrame(options_section, text="AI Validation (Advanced)",
                               font=('Segoe UI', 10, 'bold'),
                               bg='white', fg=self.colors['primary'],
                               padx=15, pady=15)
        ai_frame.pack(fill='x')
        
        ai_check = tk.Checkbutton(ai_frame, text="Enable AI validation for 95% accuracy",
                                variable=self.ai_validation_var,
                                font=('Segoe UI', 10, 'bold'), bg='white',
                                fg=self.colors['primary'],
                                command=self._on_ai_toggle)
        ai_check.pack(anchor='w', pady=5)
        
        ai_info = tk.Label(ai_frame,
                         text="Uses local Ollama AI to validate and improve extraction accuracy.\n"
                              "Requires Ollama with llama3.1:8b model to be running.",
                         font=('Segoe UI', 9), bg='white',
                         fg=self.colors['secondary'],
                         wraplength=400, justify='left')
        ai_info.pack(anchor='w', pady=(0, 10))
        
        # AI Status
        self.ai_status_label = tk.Label(ai_frame, 
                                      text="AI Status: Checking...",
                                      font=('Segoe UI', 9, 'bold'),
                                      bg='white', fg=self.colors['secondary'])
        self.ai_status_label.pack(anchor='w')
        
        # Check AI availability
        self._check_and_update_ai_status()
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(fill='x', pady=(20, 0))
        
        ok_button = tk.Button(button_frame,
                            text="OK",
                            command=lambda: self._close_options_dialog(dialog),
                            bg=self.colors['success'],
                            fg='white',
                            font=('Segoe UI', 10, 'bold'),
                            relief='flat',
                            padx=30, pady=8,
                            cursor='hand2')
        ok_button.pack(side='right', padx=(10, 0))
        
        cancel_button = tk.Button(button_frame,
                                text="Cancel",
                                command=dialog.destroy,
                                bg=self.colors['secondary'],
                                fg='white',
                                font=('Segoe UI', 10),
                                relief='flat',
                                padx=30, pady=8,
                                cursor='hand2')
        cancel_button.pack(side='right')
    
    def _close_options_dialog(self, dialog):
        """Close options dialog and update display"""
        dialog.destroy()
        self.update_settings_display()
        self.add_status_message("Processing options updated", "info")
    
    def _on_ai_toggle(self):
        """Handle AI validation toggle"""
        if self.ai_validation_var.get():
            self.add_status_message("🤖 AI validation enabled - will check availability during conversion", "info")
        else:
            self.add_status_message("📄 Using standard conversion processing", "info")
    
    def start_conversion(self):
        """Start conversion"""
        if not self.selected_files:
            messagebox.showwarning("No Files Selected", "Please select PDF files to convert.")
            return
        
        output_dir = self.output_var.get().strip()
        if not output_dir or not os.path.exists(output_dir):
            messagebox.showerror("Invalid Directory", "Please select a valid output directory.")
            return
        
        # Generate filename
        pattern = self.filename_var.get()
        current_time = datetime.datetime.now()
        filename = pattern.format(
            date=current_time.strftime("%Y%m%d"),
            time=current_time.strftime("%H%M%S")
        )
        
        if not filename.endswith('.xlsx'):
            filename += '.xlsx'
        
        output_path = os.path.join(output_dir, filename)
        
        # Update UI
        self.convert_btn.config(state='disabled', bg=self.colors['secondary'])
        self.cancel_btn.config(state='normal')
        self.progress_var.set(0)
        self.add_status_message("Starting conversion...", "info")
        
        # Start conversion thread
        self.conversion_thread = threading.Thread(
            target=self._run_conversion,
            args=(self.selected_files, output_path),
            daemon=True
        )
        self.conversion_thread.start()
    
    def _run_conversion(self, files: List[str], output_path: str):
        """Run conversion in thread"""
        try:
            # Check if AI validation is enabled
            ai_checkbox_enabled = self.ai_validation_var.get()
            ai_available = self._check_ai_availability()
            use_ai_validation = ai_checkbox_enabled and ai_available
            
            self.add_status_message(f"[DEBUG] AI checkbox: {ai_checkbox_enabled}, AI available: {ai_available}", "info")
            
            if ai_checkbox_enabled and not ai_available:
                self.add_status_message("[DEBUG] Checking why AI is not available...", "info")
                try:
                    import requests
                    response = requests.get("http://localhost:11434/api/tags", timeout=5)
                    self.add_status_message(f"[DEBUG] Ollama server response: {response.status_code}", "info")
                    if response.status_code == 200:
                        models = response.json().get('models', [])
                        model_names = [m.get('name', '') for m in models]
                        self.add_status_message(f"[DEBUG] Available models: {model_names}", "info")
                except Exception as e:
                    self.add_status_message(f"[DEBUG] Ollama connection error: {str(e)}", "info")
            
            if use_ai_validation:
                self.add_status_message("🤖 AI VALIDATION ENABLED - Using enhanced processing with Ollama AI", "success")
                self.add_status_message("⚡ This will provide up to 95% accuracy vs ~80% standard processing", "info")
                self._run_ai_enhanced_conversion(files, output_path)
            elif ai_checkbox_enabled and not ai_available:
                self.add_status_message("⚠️ AI validation requested but Ollama not available - using standard processing", "warning")
                self._run_standard_conversion(files, output_path)
            else:
                self._run_standard_conversion(files, output_path)
        except Exception as e:
            self.add_status_message(f"Conversion failed: {str(e)}", "error")
        finally:
            self.root.after(0, self._reset_buttons)
    
    def _run_standard_conversion(self, files: List[str], output_path: str):
        """Run standard conversion without AI"""
        try:
            if self.conversion_callback:
                self.add_status_message("📄 Standard conversion processing...", "info")
                # Prepare conversion settings
                conversion_settings = {
                    'include_summary': self.include_summary_var.get(),
                    'create_category_sheets': self.category_sheets_var.get(),
                    'auto_open_result': self.auto_open_var.get()
                }
                
                # Call conversion with settings
                self.conversion_callback(
                    files, 
                    output_path, 
                    self.update_progress, 
                    self.add_status_message,
                    conversion_settings
                )
                
                # Auto-open if enabled
                if conversion_settings['auto_open_result'] and os.path.exists(output_path):
                    os.startfile(output_path)
            else:
                self.add_status_message("No conversion callback set", "error")
        except Exception as e:
            self.add_status_message(f"Standard conversion failed: {str(e)}", "error")
    
    def _run_ai_enhanced_conversion(self, files: List[str], output_path: str):
        """Run AI-enhanced conversion"""
        try:
            from enhanced_main import EnhancedPDFConverter
            
            # Initialize enhanced converter
            converter = EnhancedPDFConverter(
                model=self.settings['ai_model'],
                auto_correct=self.settings['ai_auto_correct']
            )
            
            total_files = len(files)
            successful_conversions = 0
            
            for i, pdf_file in enumerate(files, 1):
                self.update_progress(i, total_files, f"Processing {os.path.basename(pdf_file)} with AI...")
                self.add_status_message(f"🔄 AI Processing: {os.path.basename(pdf_file)}", "info")
                
                try:
                    # Run enhanced conversion
                    results = converter.convert_with_validation(
                        pdf_path=pdf_file,
                        output_dir=os.path.dirname(output_path)
                    )
                    
                    if results.get('success'):
                        successful_conversions += 1
                        
                        # Update UI with results
                        summary = results.get('summary', {})
                        accuracy = summary.get('accuracy', {})
                        
                        self.add_status_message(
                            f"✅ {os.path.basename(pdf_file)}: {accuracy.get('match_rate', 0):.1f}% accuracy, "
                            f"{accuracy.get('records_captured', 0)} records captured", 
                            "success"
                        )
                        
                        # Show AI improvements
                        improvements = summary.get('improvements', {})
                        if improvements.get('corrections_applied', 0) > 0:
                            self.add_status_message(
                                f"🤖 AI ENHANCEMENT: Applied {improvements['corrections_applied']} automatic corrections",
                                "success"
                            )
                            self.add_status_message(
                                f"📈 AI improved accuracy from standard processing",
                                "success"
                            )
                        else:
                            self.add_status_message(
                                f"🎯 AI VALIDATION: No additional corrections needed - high quality extraction",
                                "success"
                            )
                    else:
                        self.add_status_message(f"❌ Failed to process {os.path.basename(pdf_file)}: {results.get('error', 'Unknown error')}", "error")
                        
                except Exception as file_error:
                    self.add_status_message(f"❌ Error processing {os.path.basename(pdf_file)}: {str(file_error)}", "error")
            
            # Auto-open file if enabled
            if self.auto_open_var.get() and os.path.exists(output_path):
                os.startfile(output_path)
                
        except ImportError:
            self.add_status_message("❌ AI components not available. Install enhanced validation system.", "error")
        except Exception as e:
            self.add_status_message(f"❌ AI conversion failed: {str(e)}", "error")
    
    def _reset_buttons(self):
        """Reset button states"""
        self.convert_btn.config(state='normal', bg=self.colors['success'])
        self.cancel_btn.config(state='disabled')
    
    def cancel_conversion(self):
        """Cancel conversion"""
        self.add_status_message("Conversion cancelled", "warning")
        self._reset_buttons()
    
    def set_conversion_callback(self, callback: Callable):
        """Set conversion callback"""
        self.conversion_callback = callback
    
    def setup_drag_drop(self):
        """Setup drag and drop functionality"""
        # Configure the drop area to accept file drops
        self.drop_frame.drop_target_register(DND_FILES)  # type: ignore
        self.drop_frame.dnd_bind('<<Drop>>', self.on_file_drop)  # type: ignore
        self.drop_frame.dnd_bind('<<DragEnter>>', self.on_drag_enter)  # type: ignore
        self.drop_frame.dnd_bind('<<DragLeave>>', self.on_drag_leave)  # type: ignore
        
        # Also bind to the labels inside the drop frame
        self.drop_icon.drop_target_register(DND_FILES)  # type: ignore
        self.drop_icon.dnd_bind('<<Drop>>', self.on_file_drop)  # type: ignore
        self.drop_icon.dnd_bind('<<DragEnter>>', self.on_drag_enter)  # type: ignore
        self.drop_icon.dnd_bind('<<DragLeave>>', self.on_drag_leave)  # type: ignore
        
        self.drop_label.drop_target_register(DND_FILES)  # type: ignore
        self.drop_label.dnd_bind('<<Drop>>', self.on_file_drop)  # type: ignore
        self.drop_label.dnd_bind('<<DragEnter>>', self.on_drag_enter)  # type: ignore
        self.drop_label.dnd_bind('<<DragLeave>>', self.on_drag_leave)  # type: ignore
    
    def on_drag_enter(self, event):
        """Handle drag enter event"""
        self.drop_frame.config(bg='#e3f2fd', relief='solid', bd=3)
        self.drop_icon.config(bg='#e3f2fd', text="📥")
        self.drop_label.config(bg='#e3f2fd', text="Drop PDF files here!", 
                              fg=self.colors['primary'], font=('Segoe UI', 10, 'bold'))
    
    def on_drag_leave(self, event):
        """Handle drag leave event"""
        self.drop_frame.config(bg='#f8fafc', relief='ridge', bd=2)
        self.drop_icon.config(bg='#f8fafc', text="📄")
        self.drop_label.config(bg='#f8fafc', text="Drag & Drop PDF files here",
                              fg=self.colors['secondary'], font=('Segoe UI', 10))
    
    def on_file_drop(self, event):
        """Handle file drop event"""
        try:
            # Get the dropped files
            files = self.root.tk.splitlist(event.data)
            pdf_files = []
            
            for file_path in files:
                file_path = file_path.strip()
                if file_path.lower().endswith('.pdf') and os.path.exists(file_path):
                    pdf_files.append(file_path)
            
            if pdf_files:
                # Add files to selection
                for pdf_file in pdf_files:
                    if pdf_file not in self.selected_files:
                        self.selected_files.append(pdf_file)
                
                # Update file list
                self.update_file_list()
                
                # Show success message
                self.add_status_message(f"Added {len(pdf_files)} PDF file(s) via drag & drop", "info")
                
                # Reset drop area appearance
                self.on_drag_leave(event)
                
                # Flash success indicator
                self.root.after(100, lambda: self.drop_frame.config(bg='#e8f5e8'))
                self.root.after(300, lambda: self.drop_frame.config(bg='#f8fafc'))
            else:
                self.add_status_message("No valid PDF files in dropped items", "error")
                
        except Exception as e:
            self.add_status_message(f"Error processing dropped files: {str(e)}", "error")
            logger.error(f"Drag and drop error: {str(e)}")

    def run(self):
        """Run the application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("GUI closed by user")

if __name__ == "__main__":
    app = SimplifiedModernGUI()
    app.run() 