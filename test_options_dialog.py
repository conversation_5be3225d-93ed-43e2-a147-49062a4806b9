#!/usr/bin/env python3
"""
Test the processing options dialog
"""

import time
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_options_dialog():
    """Test the processing options dialog"""
    print("Testing processing options dialog...")
    
    try:
        from gui_simple import SimplifiedModernGUI
        import tkinter as tk
        
        # Create GUI
        root = tk.Tk()
        gui = SimplifiedModernGUI()
        
        # Test dialog method exists
        if hasattr(gui, 'show_processing_options_dialog'):
            print("[OK] Processing options dialog method found")
        else:
            print("[ERROR] Processing options dialog method missing")
            return False
        
        # Test settings display update
        if hasattr(gui, 'update_settings_display'):
            print("[OK] Settings display update method found")
            gui.update_settings_display()
            print("[OK] Settings display updated successfully")
        else:
            print("[ERROR] Settings display update method missing")
        
        # Test AI toggle functionality
        print("\nTesting AI validation:")
        print(f"[INFO] Initial AI validation state: {gui.ai_validation_var.get()}")
        
        # Toggle AI validation
        gui.ai_validation_var.set(True)
        gui._on_ai_toggle()
        print(f"[OK] AI validation enabled: {gui.ai_validation_var.get()}")
        
        # Update display with AI enabled
        gui.update_settings_display()
        display_text = gui.settings_display.cget("text")
        print(f"[INFO] Settings display text: {display_text}")
        
        if "AI validation" in display_text:
            print("[OK] AI validation shown in settings display")
        else:
            print("[WARNING] AI validation not visible in settings display")
        
        # Clean up
        root.destroy()
        
        print("\n[SUCCESS] Dialog-based options system working")
        return True
        
    except Exception as e:
        print(f"[ERROR] Dialog test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_options_dialog()
    print(f"\nTest {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)