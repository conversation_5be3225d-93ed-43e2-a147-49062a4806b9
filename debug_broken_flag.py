#!/usr/bin/env python3
"""
Debug script to check the _broken flag behavior
"""

import pandas as pd
from data_processor import DataProcessor
from pdf_parser import PDFParser

def debug_broken_flag():
    print("=== DEBUGGING BROKEN FLAG ===")
    
    # Initialize processor and parser
    parser = PDFParser()
    processor = DataProcessor()
    
    print("1. Extracting text from PDF...")
    texts = parser.extract_text_batch(['pdfs/KOTHI SALE JULY-2025.pdf'])
    
    print("2. Processing texts to DataFrame...")
    df = processor.process_texts(texts)
    
    print("3. Analyzing DataFrame structure:")
    print(f"   Total records: {len(df)}")
    print(f"   Columns: {list(df.columns)}")
    print(f"   _broken column exists: {'_broken' in df.columns}")
    
    if '_broken' in df.columns:
        print(f"   _broken column values: {df['_broken'].value_counts()}")
        broken_records = df[df['_broken'] == 'true']
        print(f"   Records marked as broken: {len(broken_records)}")
        
        if len(broken_records) > 0:
            print("   Broken record details:")
            for idx, row in broken_records.iterrows():
                print(f"      Record {idx}: Location='{row['Location/Area']}', Property Code='{row['Property Code']}'")
    else:
        print("   ❌ _broken column not found!")
    
    print("\n4. Checking EXCEL_COLUMNS filtering...")
    from config import EXCEL_COLUMNS
    print(f"   EXCEL_COLUMNS: {EXCEL_COLUMNS}")
    print(f"   '_broken' in EXCEL_COLUMNS: {'_broken' in EXCEL_COLUMNS}")
    
    # Check if DataFrame is being filtered by EXCEL_COLUMNS
    if '_broken' in df.columns and '_broken' not in EXCEL_COLUMNS:
        print("   ⚠️ WARNING: _broken column exists but is not in EXCEL_COLUMNS!")
        print("   This means it will be removed when creating the final DataFrame!")
    
    return df

if __name__ == "__main__":
    df = debug_broken_flag() 