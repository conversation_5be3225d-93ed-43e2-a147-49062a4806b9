"""
AI-Powered Validation System using Ollama
Handles complex validation cases that pattern matching cannot resolve
"""

import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import time

from ollama_client import OllamaClient, create_ollama_client
from pattern_validator import PatternValida<PERSON>, ValidationIssue, ValidationReport

logger = logging.getLogger(__name__)

@dataclass
class AIValidationResult:
    """Result from AI validation"""
    success: bool
    corrections: List[Dict[str, Any]]
    confidence: float
    processing_time: float
    error: Optional[str] = None
    raw_response: Optional[str] = None

class AIValidator:
    """
    Intelligent validator that combines pattern matching with AI reasoning
    Uses Ollama for local, cost-free validation
    """
    
    def __init__(self, model: str = "llama3.1:8b"):
        self.pattern_validator = PatternValidator()
        self.ollama_client = create_ollama_client(model)
        self.model = model
        
        # AI validation configuration
        self.ai_config = {
            "temperature": 0.1,  # Low temperature for consistent results
            "max_tokens": 2000,
            "use_ai_threshold": 5,  # Use AI only if more than 5 complex issues
        }
        
        logger.info(f"🤖 AI Validator initialized with model: {model}")
    
    def validate_comprehensive(self, pdf_text: str, excel_records: List[Dict]) -> ValidationReport:
        """
        Perform comprehensive validation using both pattern matching and AI
        
        Args:
            pdf_text: Original PDF text
            excel_records: Generated Excel records
            
        Returns:
            Enhanced validation report with AI insights
        """
        logger.info("🔄 Starting comprehensive validation (Pattern + AI)...")
        start_time = time.time()
        
        # Phase 1: Pattern-based validation (fast, free)
        logger.info("🎯 Phase 1: Pattern-based validation...")
        pdf_records = self.pattern_validator.extract_pdf_records(pdf_text)
        pattern_report = self.pattern_validator.validate_against_excel(pdf_records, excel_records)
        
        # Phase 2: AI validation for complex issues (selective)
        ai_enhanced_issues = []
        if self._should_use_ai(pattern_report.issues):
            logger.info("🤖 Phase 2: AI validation for complex issues...")
            ai_results = self._validate_with_ai(pdf_text, excel_records, pattern_report.issues)
            
            if ai_results.success:
                ai_enhanced_issues = self._merge_ai_corrections(pattern_report.issues, ai_results.corrections)
            else:
                logger.warning(f"⚠️ AI validation failed: {ai_results.error}")
                ai_enhanced_issues = pattern_report.issues
        else:
            logger.info("ℹ️ Skipping AI validation - pattern matching sufficient")
            ai_enhanced_issues = pattern_report.issues
        
        # Phase 3: Generate enhanced report
        enhanced_report = ValidationReport(
            total_pdf_records=pattern_report.total_pdf_records,
            total_excel_records=pattern_report.total_excel_records,
            match_rate=pattern_report.match_rate,
            issues=ai_enhanced_issues,
            statistics=pattern_report.statistics,
            recommendations=self._generate_enhanced_recommendations(ai_enhanced_issues, pattern_report.recommendations)
        )
        
        processing_time = time.time() - start_time
        logger.info(f"✅ Comprehensive validation completed in {processing_time:.2f}s")
        
        return enhanced_report
    
    def _should_use_ai(self, issues: List[ValidationIssue]) -> bool:
        """Determine if AI validation is needed based on issue complexity"""
        # Use AI if we have many unresolved issues or complex patterns
        complex_issues = [
            i for i in issues 
            if i.issue_type in ['missing_record', 'location_mismatch'] and i.severity in ['critical', 'high']
        ]
        
        should_use = len(complex_issues) >= self.ai_config["use_ai_threshold"]
        
        if should_use:
            logger.info(f"🤖 AI validation triggered: {len(complex_issues)} complex issues found")
        else:
            logger.info(f"🎯 Pattern validation sufficient: {len(complex_issues)} complex issues")
        
        return should_use
    
    def _validate_with_ai(self, pdf_text: str, excel_records: List[Dict], issues: List[ValidationIssue]) -> AIValidationResult:
        """Use Ollama to validate complex issues"""
        if not self.ollama_client.is_available():
            return AIValidationResult(
                success=False,
                corrections=[],
                confidence=0.0,
                processing_time=0.0,
                error="Ollama server not available"
            )
        
        start_time = time.time()
        
        # Prepare AI validation prompt
        validation_prompt = self._create_validation_prompt(pdf_text, excel_records, issues)
        
        # Get AI response
        response = self.ollama_client.generate(
            prompt=validation_prompt,
            system_prompt=self._get_system_prompt(),
            **self.ai_config
        )
        
        processing_time = time.time() - start_time
        
        if not response.success:
            return AIValidationResult(
                success=False,
                corrections=[],
                confidence=0.0,
                processing_time=processing_time,
                error=response.error
            )
        
        # Parse AI response
        try:
            ai_result = json.loads(response.content)
            
            return AIValidationResult(
                success=True,
                corrections=ai_result.get("corrections", []),
                confidence=ai_result.get("summary", {}).get("confidence_score", 0.0),
                processing_time=processing_time,
                raw_response=response.content
            )
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse AI response: {str(e)}")
            
            # Try to extract corrections from unstructured response
            fallback_corrections = self._extract_fallback_corrections(response.content)
            
            return AIValidationResult(
                success=len(fallback_corrections) > 0,
                corrections=fallback_corrections,
                confidence=0.5,  # Lower confidence for fallback parsing
                processing_time=processing_time,
                error=f"JSON parse error, using fallback: {str(e)}",
                raw_response=response.content
            )
    
    def _create_validation_prompt(self, pdf_text: str, excel_records: List[Dict], issues: List[ValidationIssue]) -> str:
        """Create a comprehensive validation prompt for AI"""
        
        # Prepare issue summary for AI
        issue_summary = []
        for issue in issues[:10]:  # Limit to first 10 issues to stay within token limits
            issue_summary.append({
                "type": issue.issue_type,
                "severity": issue.severity,
                "description": issue.description,
                "line_number": issue.line_number,
                "expected": issue.expected_value,
                "actual": issue.actual_value
            })
        
        # Sample PDF text (first 2000 chars to stay within limits)
        pdf_sample = pdf_text[:2000] if len(pdf_text) > 2000 else pdf_text
        
        # Sample Excel records (first 5)
        excel_sample = excel_records[:5] if len(excel_records) > 5 else excel_records
        
        return f"""
PROPERTY DATA VALIDATION TASK

I need you to analyze property extraction issues and provide specific corrections.

PDF TEXT SAMPLE:
{pdf_sample}

EXCEL RECORDS SAMPLE:
{json.dumps(excel_sample, indent=2)}

VALIDATION ISSUES FOUND:
{json.dumps(issue_summary, indent=2)}

ANALYSIS REQUIRED:
1. Review missing records and suggest where they should be placed
2. Identify location assignment errors and provide corrections
3. Check for property code extraction issues
4. Validate size and contact information accuracy

Provide your response in this exact JSON format:
{{
    "corrections": [
        {{
            "issue_id": "missing_record_1",
            "issue_type": "missing_record",
            "description": "Property G-50 200Y in East of Kailash missing from Excel",
            "suggested_fix": {{
                "action": "add_record",
                "property_code": "G-50",
                "location": "East of Kailash",
                "size": "200Y",
                "property_type": "Old House",
                "details": "GF+FF+SF 3BHK",
                "contact_person": "RAKESH GUPTA",
                "phone": "9810014028"
            }},
            "confidence": 0.95,
            "reasoning": "Clear property record found in PDF line 24"
        }}
    ],
    "summary": {{
        "total_issues_analyzed": 10,
        "corrections_provided": 8,
        "confidence_score": 0.87,
        "recommendations": [
            "Fix location context tracking in data processor",
            "Improve property code extraction patterns"
        ]
    }}
}}

Focus on providing actionable, specific corrections with high confidence.
"""
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for AI validation"""
        return """You are an expert real estate data validation specialist with deep knowledge of Indian property markets.

Your expertise includes:
- Indian property naming conventions (A-123, B-456, G-50 format)
- Delhi NCR locations (East of Kailash, Maharani Bagh, Defence Colony, etc.)
- Property types (Kothi, Duplex, Plot, House, Flat, Shop, Bungalow)
- Size measurements (Yards, Square Feet, Acres, Marla, Kanal)
- Building structures (GF+FF+SF = Ground+First+Second Floor, BMT = Basement)
- Indian contact patterns (names and 10-digit phone numbers)

Key principles:
1. Accuracy is paramount - only suggest corrections you're confident about
2. Maintain consistency with Indian real estate terminology
3. Preserve original property codes and location names exactly as written
4. Focus on critical issues first (missing records, location errors)
5. Provide specific, actionable corrections in valid JSON format

Always respond with properly formatted JSON containing corrections array and summary object."""
    
    def _extract_fallback_corrections(self, raw_response: str) -> List[Dict[str, Any]]:
        """Extract corrections from unstructured AI response as fallback"""
        corrections = []
        
        # Try to find property codes, locations, and other structured data
        # This is a simplified fallback - in practice, you'd implement more sophisticated parsing
        lines = raw_response.split('\n')
        
        current_correction = {}
        for line in lines:
            line = line.strip()
            
            # Look for property codes
            if 'property_code' in line.lower() and ':' in line:
                try:
                    value = line.split(':', 1)[1].strip().strip('"\'')
                    current_correction['property_code'] = value
                except:
                    pass
            
            # Look for locations
            elif 'location' in line.lower() and ':' in line:
                try:
                    value = line.split(':', 1)[1].strip().strip('"\'')
                    current_correction['location'] = value
                except:
                    pass
            
            # If we have enough data for a correction, add it
            if len(current_correction) >= 2:
                corrections.append({
                    'issue_type': 'ai_fallback',
                    'suggested_fix': current_correction.copy(),
                    'confidence': 0.5
                })
                current_correction = {}
        
        logger.info(f"🔄 Extracted {len(corrections)} fallback corrections from unstructured response")
        return corrections
    
    def _merge_ai_corrections(self, pattern_issues: List[ValidationIssue], ai_corrections: List[Dict]) -> List[ValidationIssue]:
        """Merge AI corrections with pattern-based issues"""
        enhanced_issues = pattern_issues.copy()
        
        # Create a map of AI corrections by issue type
        ai_correction_map = {}
        for correction in ai_corrections:
            issue_type = correction.get('issue_type', 'unknown')
            if issue_type not in ai_correction_map:
                ai_correction_map[issue_type] = []
            ai_correction_map[issue_type].append(correction)
        
        # Enhance existing issues with AI suggestions
        for issue in enhanced_issues:
            matching_corrections = ai_correction_map.get(issue.issue_type, [])
            
            for correction in matching_corrections:
                # Check if this correction applies to this specific issue
                if self._correction_matches_issue(issue, correction):
                    # Update the issue with AI suggestions
                    if correction.get('suggested_fix'):
                        issue.suggested_fix = correction['suggested_fix']
                    
                    # Update confidence if AI provides higher confidence
                    ai_confidence = correction.get('confidence', 0.0)
                    if ai_confidence > issue.confidence:
                        issue.confidence = ai_confidence
                    
                    break
        
        # Add new issues discovered by AI
        for issue_type, corrections in ai_correction_map.items():
            for correction in corrections:
                # Check if this is a new issue not covered by pattern matching
                if not any(self._correction_matches_issue(issue, correction) for issue in enhanced_issues):
                    new_issue = ValidationIssue(
                        issue_type=correction.get('issue_type', 'ai_discovered'),
                        severity='medium',  # Default severity for AI-discovered issues
                        description=correction.get('description', 'AI-discovered issue'),
                        suggested_fix=correction.get('suggested_fix'),
                        confidence=correction.get('confidence', 0.7)
                    )
                    enhanced_issues.append(new_issue)
        
        logger.info(f"🔄 Enhanced {len(pattern_issues)} pattern issues with {len(ai_corrections)} AI corrections")
        return enhanced_issues
    
    def _correction_matches_issue(self, issue: ValidationIssue, correction: Dict) -> bool:
        """Check if an AI correction matches a specific issue"""
        # Simple matching based on issue type and key identifiers
        if issue.issue_type != correction.get('issue_type'):
            return False
        
        # For missing records, match on property code or line number
        if issue.issue_type == 'missing_record':
            suggested_fix = correction.get('suggested_fix', {})
            issue_prop_code = getattr(issue, 'property_code', None) or (
                issue.suggested_fix.get('record_data', {}).get('property_code') if issue.suggested_fix else None
            )
            correction_prop_code = suggested_fix.get('property_code')
            
            return issue_prop_code == correction_prop_code
        
        # For location mismatches, match on property code
        elif issue.issue_type == 'location_mismatch':
            suggested_fix = correction.get('suggested_fix', {})
            issue_prop_code = issue.suggested_fix.get('property_code') if issue.suggested_fix else None
            correction_prop_code = suggested_fix.get('property_code')
            
            return issue_prop_code == correction_prop_code
        
        # Default: assume it matches if types are the same
        return True
    
    def _generate_enhanced_recommendations(self, issues: List[ValidationIssue], base_recommendations: List[str]) -> List[str]:
        """Generate enhanced recommendations based on AI insights"""
        recommendations = base_recommendations.copy()
        
        # Analyze AI-enhanced issues for patterns
        ai_issues = [i for i in issues if hasattr(i, 'confidence') and i.confidence > 0.8]
        high_confidence_fixes = len([i for i in ai_issues if i.suggested_fix])
        
        if high_confidence_fixes > 5:
            recommendations.append(f"🤖 AI identified {high_confidence_fixes} high-confidence fixes ready for auto-correction")
        
        # Add AI-specific recommendations
        missing_with_fixes = len([i for i in issues if i.issue_type == 'missing_record' and i.suggested_fix])
        if missing_with_fixes > 10:
            recommendations.append(f"📝 Consider implementing auto-correction for {missing_with_fixes} missing records with AI suggestions")
        
        return recommendations
    
    def apply_corrections(self, excel_records: List[Dict], issues: List[ValidationIssue]) -> Tuple[List[Dict], int]:
        """
        Apply AI-suggested corrections to Excel records
        
        Args:
            excel_records: Original Excel records
            issues: Issues with suggested fixes
            
        Returns:
            Tuple of (corrected_records, corrections_applied)
        """
        corrected_records = excel_records.copy()
        corrections_applied = 0
        
        logger.info(f"🔧 Applying corrections for {len(issues)} issues...")
        
        for issue in issues:
            if not issue.suggested_fix or issue.confidence < 0.7:
                continue
            
            try:
                if issue.issue_type == 'missing_record':
                    # Add missing record
                    new_record = self._create_record_from_fix(issue.suggested_fix)
                    corrected_records.append(new_record)
                    corrections_applied += 1
                    logger.debug(f"✅ Added missing record: {new_record.get('Property Code', 'N/A')}")
                
                elif issue.issue_type == 'location_mismatch':
                    # Fix location mismatch
                    property_code = issue.suggested_fix.get('property_code')
                    correct_location = issue.suggested_fix.get('correct_location')
                    
                    for record in corrected_records:
                        if record.get('Property Code') == property_code:
                            record['Location/Area'] = correct_location
                            corrections_applied += 1
                            logger.debug(f"✅ Fixed location for {property_code}: {correct_location}")
                            break
                
                # Add more correction types as needed
                
            except Exception as e:
                logger.error(f"❌ Failed to apply correction for {issue.issue_type}: {str(e)}")
        
        logger.info(f"✅ Applied {corrections_applied} corrections to {len(corrected_records)} records")
        return corrected_records, corrections_applied
    
    def _create_record_from_fix(self, suggested_fix: Dict) -> Dict[str, Any]:
        """Create a new Excel record from AI suggested fix"""
        # Extract the action and record data
        action = suggested_fix.get('action')
        
        if action == 'add_record':
            # Create record from individual fields
            return {
                'Property Code': suggested_fix.get('property_code', ''),
                'Location/Area': suggested_fix.get('location', ''),
                'Size/Yards': suggested_fix.get('size', ''),
                'Property Type': suggested_fix.get('property_type', ''),
                'Details': suggested_fix.get('details', ''),
                'Contact Person': suggested_fix.get('contact_person', ''),
                'Phone Numbers': suggested_fix.get('phone', ''),
                'Status': '',
                'File Source': 'AI_Correction',
                'Property Category': 'Kothi Sale',
                'Month/Year': 'July 2025'
            }
        elif 'record_data' in suggested_fix:
            # Create record from record_data
            record_data = suggested_fix['record_data']
            return {
                'Property Code': record_data.get('property_code', ''),
                'Location/Area': record_data.get('location', ''),
                'Size/Yards': record_data.get('size', ''),
                'Property Type': record_data.get('property_type', ''),
                'Details': record_data.get('original_line', ''),
                'Contact Person': record_data.get('contact_name', ''),
                'Phone Numbers': record_data.get('phone', ''),
                'Status': '',
                'File Source': 'AI_Correction',
                'Property Category': 'Kothi Sale',
                'Month/Year': 'July 2025'
            }
        
        return {}

if __name__ == "__main__":
    # Test the AI validator
    logging.basicConfig(level=logging.INFO)
    
    validator = AIValidator()
    
    # Test connection
    test_result = validator.ollama_client.test_connection()
    print(f"AI Validator test: {json.dumps(test_result, indent=2)}")