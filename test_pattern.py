import re

# The actual location pattern from config.py
location_pattern = re.compile(r'^([A-Z](?:[A-Z\s\.\-–\d]*?)(?:\s+(?:MARKET|MKT|COLONY|ENCLAVE|PARK|NAGAR|BLOCK|BLK|BAGH|VIHAR|KHAS|PLACE|EX|EXTN|EXTENSION|ROAD|RD|LANE|WEST|EAST|NORTH|SOUTH|CENTRAL|NEW|OLD)(?:\s*[-–\d]+)?)+|(?:[A-Z][A-Z\s\.\-–]{4,}[A-Z\d])(?:\s*[-–\d]+)?)[-–\s]*(?=\s|$)', re.MULTILINE)

test_line = "G-50 200Y Old"
print(f"Testing line: '{test_line}'")

match = location_pattern.match(test_line)
if match:
    print(f"LOCATION PATTERN MATCHED: '{match.group(1)}'")
    print("This explains why the line is treated as a location!")
else:
    print("Location pattern did not match")

# Let's also test some valid location headers
print("\nTesting valid location headers:")
valid_locations = ["NEW FRIENDS COLONY", "MAHARANI BAGH", "SOUTH DELHI"]
for loc in valid_locations:
    match = location_pattern.match(loc)
    if match:
        print(f"'{loc}' -> MATCHED: '{match.group(1)}'")
    else:
        print(f"'{loc}' -> No match") 