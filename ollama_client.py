"""
Ollama Client for Local AI Validation
Provides cost-free AI validation using locally hosted models
"""

import json
import logging
import requests
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class OllamaResponse:
    """Structure for Ollama API responses"""
    content: str
    success: bool
    error: Optional[str] = None
    processing_time: float = 0.0

class OllamaClient:
    """
    Local AI client for property data validation using Ollama
    """
    
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "llama3.1:8b"):
        self.base_url = base_url
        self.model = model
        self.session = requests.Session()
        self.session.timeout = 30
        
        # Model configuration
        self.default_options = {
            "temperature": 0.1,  # Low temperature for consistent results
            "top_p": 0.9,
            "max_tokens": 1000,
            "stop": ["Human:", "Assistant:", "User:"]
        }
        
        logger.info(f"🤖 Ollama client initialized: {base_url} | Model: {model}")
    
    def is_available(self) -> bool:
        """Check if Ollama server is running and model is available"""
        try:
            # Check server status
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code != 200:
                return False
            
            # Check if our model is available
            models = response.json().get("models", [])
            model_names = [model["name"] for model in models]
            
            if self.model not in model_names:
                logger.warning(f"⚠️ Model {self.model} not found. Available models: {model_names}")
                return False
            
            logger.info(f"✅ Ollama server available with model {self.model}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Ollama server not available: {str(e)}")
            return False
    
    def generate(self, prompt: str, system_prompt: str = None, **kwargs) -> OllamaResponse:
        """
        Generate response using Ollama model
        
        Args:
            prompt: User prompt
            system_prompt: Optional system prompt for context
            **kwargs: Additional generation parameters
            
        Returns:
            OllamaResponse with generated content
        """
        start_time = time.time()
        
        try:
            # Prepare the request
            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {**self.default_options, **kwargs}
            }
            
            # Add system prompt if provided
            if system_prompt:
                data["system"] = system_prompt
            
            # Make the request
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=data
            )
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("response", "").strip()
                
                logger.debug(f"🤖 Ollama response ({processing_time:.2f}s): {content[:100]}...")
                
                return OllamaResponse(
                    content=content,
                    success=True,
                    processing_time=processing_time
                )
            else:
                error = f"HTTP {response.status_code}: {response.text}"
                logger.error(f"❌ Ollama API error: {error}")
                
                return OllamaResponse(
                    content="",
                    success=False,
                    error=error,
                    processing_time=processing_time
                )
                
        except Exception as e:
            processing_time = time.time() - start_time
            error = f"Request failed: {str(e)}"
            logger.error(f"❌ Ollama request error: {error}")
            
            return OllamaResponse(
                content="",
                success=False,
                error=error,
                processing_time=processing_time
            )
    
    def validate_property_data(self, pdf_text: str, excel_records: List[Dict], issues: List[Dict]) -> Dict[str, Any]:
        """
        Use local AI to validate property data and suggest corrections
        
        Args:
            pdf_text: Original PDF text
            excel_records: Generated Excel records
            issues: List of validation issues found
            
        Returns:
            Validation results with suggested corrections
        """
        system_prompt = """You are an expert real estate data validator specializing in Indian property records.

Your task is to analyze property extraction issues and provide accurate corrections.

Key principles:
1. Property codes follow patterns like: A-123, B-456, G-50, etc.
2. Sizes are in yards (Y), square feet (SQ FT), or acres
3. Common Indian locations: East of Kailash, Maharani Bagh, Defence Colony, etc.
4. Property types: Kothi, Duplex, Plot, House, Shop, etc.
5. Structural details: GF+FF+SF (Ground+First+Second Floor), BMT (Basement), etc.

Respond in JSON format with specific corrections."""
        
        # Prepare validation prompt
        prompt = f"""
Analyze these property data extraction issues and provide corrections:

ORIGINAL PDF TEXT SAMPLE:
{pdf_text[:1000]}...

ISSUES FOUND:
{json.dumps(issues, indent=2)}

CURRENT EXCEL RECORDS (first 5):
{json.dumps(excel_records[:5], indent=2)}

Provide corrections in this JSON format:
{{
    "corrections": [
        {{
            "issue_type": "missing_record|location_mismatch|property_code_error",
            "description": "Brief description of the issue",
            "suggested_fix": {{
                "property_code": "A-123",
                "location": "East of Kailash",
                "size": "200Y",
                "details": "GF+FF+SF 3BHK"
            }},
            "confidence": 0.9
        }}
    ],
    "summary": {{
        "total_issues": 5,
        "fixable_issues": 4,
        "confidence_score": 0.85
    }}
}}
"""
        
        logger.info(f"🤖 Validating {len(issues)} issues with local AI...")
        
        response = self.generate(prompt, system_prompt)
        
        if not response.success:
            logger.error(f"❌ AI validation failed: {response.error}")
            return {
                "corrections": [],
                "summary": {"total_issues": len(issues), "fixable_issues": 0, "confidence_score": 0.0},
                "error": response.error
            }
        
        try:
            # Parse JSON response
            result = json.loads(response.content)
            logger.info(f"✅ AI validation completed: {result.get('summary', {})}")
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse AI response as JSON: {str(e)}")
            logger.debug(f"Raw response: {response.content}")
            
            # Fallback: extract any useful information from response
            return {
                "corrections": [],
                "summary": {"total_issues": len(issues), "fixable_issues": 0, "confidence_score": 0.0},
                "error": f"JSON parse error: {str(e)}",
                "raw_response": response.content
            }
    
    def batch_validate(self, validation_tasks: List[Dict]) -> List[Dict]:
        """
        Process multiple validation tasks efficiently
        
        Args:
            validation_tasks: List of validation tasks
            
        Returns:
            List of validation results
        """
        results = []
        
        logger.info(f"🔄 Processing {len(validation_tasks)} validation tasks...")
        
        for i, task in enumerate(validation_tasks):
            logger.debug(f"Processing task {i+1}/{len(validation_tasks)}")
            
            result = self.validate_property_data(
                task.get("pdf_text", ""),
                task.get("excel_records", []),
                task.get("issues", [])
            )
            
            results.append({
                "task_id": task.get("id", i),
                "result": result
            })
            
            # Small delay to prevent overwhelming the local model
            time.sleep(0.1)
        
        logger.info(f"✅ Batch validation completed: {len(results)} results")
        return results
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Test the Ollama connection and model capabilities
        
        Returns:
            Test results
        """
        logger.info("🧪 Testing Ollama connection and model...")
        
        if not self.is_available():
            return {
                "success": False,
                "error": "Ollama server or model not available"
            }
        
        # Test with a simple property validation task
        test_prompt = """
Test validation task: 
Given this property line: "G-50 200Y OLD HOUSE (RAKESH GUPTA 9810014028)"
Extract: {"property_code": "G-50", "size": "200Y", "type": "Old House", "contact": "RAKESH GUPTA"}
"""
        
        response = self.generate(test_prompt)
        
        return {
            "success": response.success,
            "response_time": response.processing_time,
            "model": self.model,
            "test_response": response.content[:200] if response.success else None,
            "error": response.error
        }

# Factory function for easy initialization
def create_ollama_client(model: str = "llama3.1:8b") -> OllamaClient:
    """
    Factory function to create and test Ollama client
    
    Args:
        model: Model name to use
        
    Returns:
        Configured OllamaClient
    """
    client = OllamaClient(model=model)
    
    # Test connection
    test_result = client.test_connection()
    
    if test_result["success"]:
        logger.info(f"✅ Ollama client ready: {model} ({test_result['response_time']:.2f}s response time)")
    else:
        logger.error(f"❌ Ollama client failed: {test_result.get('error', 'Unknown error')}")
    
    return client

if __name__ == "__main__":
    # Test the Ollama client
    logging.basicConfig(level=logging.INFO)
    
    client = create_ollama_client()
    
    # Run test
    test_result = client.test_connection()
    print(f"Test result: {json.dumps(test_result, indent=2)}")