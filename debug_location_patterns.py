import re

def test_location_patterns():
    test_lines = [
        "G-50 200Y Old",
        "E-147 Amar Colony",
        "A-287 500Y BMT+GF+FF+SF+TF 3BHK",
        "NEW FRIENDS COLONY",
        "MAHARANI BAGH"
    ]
    
    # The actual location pattern from config.py
    location_pattern = re.compile(r'^([A-Z](?:[A-Z\s\.\-–\d]*?)(?:\s+(?:MARKET|MKT|COLONY|ENCLAVE|PARK|NAGAR|BLOCK|BLK|BAGH|VIHAR|KHAS|PLACE|EX|EXTN|EXTENSION|ROAD|RD|LANE|WEST|EAST|NORTH|SOUTH|CENTRAL|NEW|OLD)(?:\s*[-–\d]+)?)+|(?:[A-Z][A-Z\s\.\-–]{4,}[A-Z\d])(?:\s*[-–\d]+)?)[-–\s]*(?=\s|$)', re.MULTILINE)
    
    # Embedded location patterns used in _extract_embedded_location
    embedded_location_patterns = [
        r'\b(NEW FRIENDS COLONY|MAHARANI BAGH|DEFENCE COLONY|LAJPAT NAGAR|GREATER KAILASH)\b',
        r'\b([A-Z]+\s+(?:COLONY|NAGAR|BAGH|ENCLAVE|PARK|VIHAR))\b',
        r'\b(SOUTH DELHI|CENTRAL DELHI|EAST DELHI|WEST DELHI|NORTH DELHI)\b',
        r'\b([A-Z]-?BLOCK|[A-Z]-?BLK)\b',
    ]
    
    print("Testing location pattern matching:")
    print("=" * 60)
    
    for line in test_lines:
        print(f'\nTesting line: "{line}"')
        
        # Test main location pattern (the one causing the issue)
        location_match = location_pattern.match(line)
        if location_match:
            print(f'  MAIN LOCATION PATTERN MATCHED: "{location_match.group(1)}"')
            print(f'  → THIS IS WHY "{line}" IS TREATED AS A LOCATION!')
        else:
            print(f'  Main location pattern: No match')
        
        # Test embedded location patterns
        print(f'  Embedded location patterns:')
        found_embedded = False
        for i, pattern in enumerate(embedded_location_patterns):
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                print(f'    Pattern {i+1} MATCHED: "{match.group(1)}"')
                found_embedded = True
            else:
                print(f'    Pattern {i+1}: No match')
        
        if not found_embedded:
            print("    → NO EMBEDDED LOCATION PATTERNS MATCHED")

if __name__ == "__main__":
    test_location_patterns() 