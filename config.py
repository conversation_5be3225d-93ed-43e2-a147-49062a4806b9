"""
Configuration file for PDF to Excel Converter
Contains all settings, patterns, and column definitions
"""

import re

# Excel Column Definitions
EXCEL_COLUMNS = [
    'File Source',
    'Property Category', 
    'Month/Year',
    'Location/Area',
    'Property Code',
    'Size/Yards',
    'Property Type',
    'Details',
    'Contact Person',
    'Phone Numbers',
    'Status'
]

# Property Categories (extracted from filename)
PROPERTY_CATEGORIES = {
    'COMM RENT': 'Commercial Rent',
    'COMM SALE': 'Commercial Sale', 
    'KOTHI RENT': 'Kothi Rent',
    'KOTHI SALE': 'Kothi Sale',
    'RESI RENT': 'Residential Rent',
    'RESI SALE': 'Residential Sale'
}

# Regex Patterns for Data Extraction (Optimized and Enhanced)
PATTERNS = {
    # ENHANCED location pattern - match location headers even with property data after them
    'location': re.compile(r'^([A-Z][\w\s\.\-–]*(?:ROAD|NAGAR|PARK|VIHAR|COLONY|ENCLAVE|MARKET|MKT|BLOCK|BLK|EXTENSION|EXTN|BAGH|PLACE|LANE|WEST|EAST|NORTH|SOUTH|CENTRAL|KAILASH|OF|GREATER|FARM|KUNJ|KHAS)(?:\s*[-\d]+)?|SOUTH\s+EX[-\d]*|[A-Z][\w\s\.\-–]{4,}(?:COLONY|PARK|NAGAR|ENCLAVE|MARKET|VIHAR|BAGH|KHAS)|G\.K[-\d]+|[A-Z][\w\s\.\-–]{5,}?)[-–\s]*', re.MULTILINE),
    
    # Optimized property code patterns - more accurate with better boundary detection
    'property_code': re.compile(r'\b(?:SHOP\s+NO[-\s]*\d+[A-Z]*(?:\s*[&+]\s*\d+[A-Z]*)*|[A-Z][-/]\d+[A-Z]*|(?<!\w)[A-Z]{1,4}[-/]?\d+[A-Z]*(?!\w)|[A-Z]*\d+[/-][A-Z]\d+[A-Z]*|(?:^|\s)([A-Z]{1,2}[-\s]?BLK|[A-Z]{1,2}[-\s]?\d{1,4})(?=\s|$))\b', re.MULTILINE),
    
    # Enhanced size pattern - better handling of Indian measurement units and combinations
    'size_yards': re.compile(r'(\d+(?:\.\d+)?(?:\s*(?:FT|Y|YD|YARDS?|ACRES?|ACRE|SCRE|SQY|SQ\.?FT|SQ\.?Y|MARLA|KANAL))(?:\s*[+&×xX]\s*\d+(?:\.\d+)?(?:\s*(?:FT|Y|YD|YARDS?|ACRES?|ACRE|SCRE|SQY|SQ\.?FT|SQ\.?Y|MARLA|KANAL)))*)', re.IGNORECASE),
    
    # Enhanced contact person pattern - better name extraction
    'contact_person': re.compile(r'\(([^)]*?)(?:\s+\d{10}|\s+\d{3}[-\s]?\d{7,8}|\s+011[-\s]?\d{8})', re.IGNORECASE),
    
    # Optimized phone numbers pattern - comprehensive Indian number formats
    'phone_numbers': re.compile(r'(\d{10}|9\d{9}|[6-9]\d{9}|\d{3}[-\s]?\d{7}|\d{4}[-\s]?\d{6}|011[-\s]?\d{8}|\+91[-\s]?\d{10})'),
    
    # Enhanced status pattern - more comprehensive property status indicators
    'status': re.compile(r'(?:P/D|PD|NOT\s+CONFIRM(?:ED)?|MANDATE|FREE\s+HOLD|LEASE\s+HOLD|ALSO\s+RENT|WOT\s+LIFT|WITHOUT\s+LIFT|OUTRIGHT|AVAILABLE|SOLD|RENTED|VACANT|OCCUPIED)', re.IGNORECASE),
    
    # Enhanced property type pattern - more property types including Indian variants
    'property_type': re.compile(r'(DUPLEX|BUNGLOW|BUNGALOW|KOTHI|HOUSE|PLOT|FARM\s*HOUSE?|OLD\s+HOUSE|SHOP|SHOWROOM|INDUSTRIAL\s+PLOT|VILLA|FLAT|APARTMENT|PENTHOUSE|STUDIO|COMMERCIAL\s+SPACE)', re.IGNORECASE),
    
    # Enhanced details pattern - comprehensive building and property details
    'details': re.compile(r'(BMT(?:\+[A-Z]+)*|LGF|UGF|GF(?:\+[A-Z]+)*|FF(?:\+[A-Z]+)*|SF(?:\+[A-Z]+)*|TF(?:\+[A-Z]+)*|\d+BHK|\d+BR|CRNR|CORNER|PARK\s+FAC(?:ING)?|EAST\s+FAC(?:ING)?|WEST\s+FAC(?:ING)?|NORTH\s+FAC(?:ING)?|SOUTH\s+FAC(?:ING)?|WIDE\s+ROAD|MIX\s+LAND\s+USE|MIXED\s+LAND|\d+\s*YRS?\s+OLD|MNRD|MAIN\s+ROAD|NEAR\s+METRO|METRO\s+NEAR|NEW\s+CONST(?:RUCTION)?)', re.IGNORECASE),
    
    # Enhanced month/year pattern - more date formats
    'month_year': re.compile(r'(JANUARY|FEBRUARY|MARCH|APRIL|MAY|JUNE|JULY|AUGUST|SEPTEMBER|OCTOBER|NOVEMBER|DECEMBER|JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)[\s-]*(\d{4}|\d{2})', re.IGNORECASE),
    
    # Enhanced additional details pattern - more comprehensive property features
    'additional_details': re.compile(r'(\d+\s*YRS?\s+OLD|NEW\s+MKT|EACH|WOT\s+LIFT|WITHOUT\s+LIFT|FOR\s+SHOWROOM|NEAR\s+METRO|MAIN\s+ROAD|COVERED\s+AREA|CARPET\s+AREA|BUILT\s+UP|UNFURNISHED|FURNISHED|SEMI\s+FURNISHED|PARKING|CAR\s+PARK)', re.IGNORECASE),
    
    # Enhanced area/location within line pattern - better locality detection
    'area_in_line': re.compile(r'\b([A-Z][A-Z\s]{2,}(?:EXTN|EXTENSION|NAGAR|COLONY|VIHAR|ENCLAVE|BLOCK|BLK|PARK|GARDEN|MARKET|MKT|PHASE|SECTOR)[\s\d]*)\b', re.IGNORECASE),
    
    # Enhanced size detection to avoid misclassification as property codes
    'size_only': re.compile(r'^\d+(?:\.\d+)?(?:\s*(?:FT|Y|YD|YARDS?|ACRES?|ACRE|SCRE|SQY|SQ\.?FT|SQ\.?Y|MARLA|KANAL))\s*(?:GF|FF|SF|TF|EA|EACH)?$', re.IGNORECASE),
    
    # New pattern for better property code detection at line start
    'line_start_code': re.compile(r'^([A-Z]{1,3}[-/]?\d+[A-Z]*|SHOP\s+NO\s*\d+)\s+', re.IGNORECASE),
    
    # Enhanced building structure pattern for better detail extraction
    'building_structure': re.compile(r'((?:BMT|LGF|UGF|GF|FF|SF|TF)(?:\s*\+\s*(?:BMT|LGF|UGF|GF|FF|SF|TF))*)', re.IGNORECASE),
    
    # Pattern for BHK and bedroom information
    'bedroom_info': re.compile(r'(\d+\s*(?:BHK|BR|BEDROOM|BED))', re.IGNORECASE),
    
    # Pattern for facing/orientation information
    'facing_info': re.compile(r'((?:EAST|WEST|NORTH|SOUTH|PARK|ROAD|MAIN\s+ROAD)\s+(?:FAC|FACING))', re.IGNORECASE),
    
    # Pattern for commercial property specifics
    'commercial_details': re.compile(r'(FRONT\s+(?:SHOP|OFFICE)|DOUBLE\s+HEIGHT|MEZZANINE|AC|CENTRALLY\s+AC|FURNISHED|UNFURNISHED)', re.IGNORECASE),
    
    # ==================== ENHANCED PATTERNS FOR MAXIMUM DATA EXTRACTION ====================
    
    # Enhanced location pattern - more precise to avoid catching property codes
    'location_precise': re.compile(r'^([A-Z][A-Z\s\.\-–]*(?:COLONY|NAGAR|BAGH|ENCLAVE|PARK|VIHAR|MARKET|BLOCK|EXTENSION|EXTN|EAST|WEST|NORTH|SOUTH|CENTRAL)[\s\d\-–]*)\s*[-–]*\s*$', re.MULTILINE),
    
    # Pattern to catch property codes that look like locations
    'property_disguised_as_location': re.compile(r'^([A-Z]{1,3}[-/]?\d+[A-Z]*)\s+(\d+Y?\s+.+)$'),
    
    # Enhanced continuation patterns for better line merging
    'strong_continuation': re.compile(r'^(?:\d{10}|9\d{9}|\(.*\)|P/D|REALTY|ESTATE|BMT\+|GF\+)'),
    
    # Fragment recovery patterns for aggressive data capture
    'any_property_code': re.compile(r'\b[A-Z]{1,3}[-/]?\d+[A-Z]*\b'),
    'any_size': re.compile(r'\b\d+(?:\.\d+)?(?:Y|FT|ACRES?)\b', re.IGNORECASE),
    'any_phone': re.compile(r'\b(?:9\d{9}|\d{10}|011-\d{8})\b'),
    
    # Perfect pattern matching for high-confidence extraction
    'perfect_property_pattern': re.compile(r'^([A-Z]{1,3}[-/]?\d+[A-Z]*)\s+(\d+(?:\.\d+)?Y)\s+(.+)', re.IGNORECASE),
    
    # Enhanced property code patterns with better boundary detection
    'property_code_enhanced': re.compile(r'\b(?:SHOP\s+NO[-\s]*\d+[A-Z]*|[A-Z]{1,3}[-/]?\d+[A-Z]*|[A-Z]-?BLOCK)\b', re.IGNORECASE),
    
    # Smart size detection with context
    'size_with_context': re.compile(r'(\d+(?:\.\d+)?)\s*(?:Y|YD|YARDS?|SQ\.?\s*(?:FT|Y)|FT|ACRES?)\b', re.IGNORECASE),
    
    # Enhanced contact patterns
    'contact_enhanced': re.compile(r'\(([^)]*?(?:[A-Z]{2,}[^)]*?))\)', re.IGNORECASE),
    
    # Building structure patterns
    'building_structure_enhanced': re.compile(r'((?:BMT|LGF|UGF|GF|FF|SF|TF)(?:\s*\+\s*(?:BMT|LGF|UGF|GF|FF|SF|TF))*)', re.IGNORECASE),
    
    # BHK and bedroom patterns
    'bedroom_enhanced': re.compile(r'(\d+\s*(?:BHK|BR|BEDROOM))', re.IGNORECASE),
    
    # Property orientation patterns
    'orientation_enhanced': re.compile(r'((?:EAST|WEST|NORTH|SOUTH|PARK|MAIN\s+ROAD)\s+(?:FACING|FAC))', re.IGNORECASE),
    
    # Property condition patterns
    'condition_enhanced': re.compile(r'(\d+\s*YRS?\s+OLD|NEW\s+CONST(?:RUCTION)?|FURNISHED|UNFURNISHED)', re.IGNORECASE),
    
    # Status indicators enhanced
    'status_enhanced': re.compile(r'(P/D|LEASE\s+HOLD|FREE\s+HOLD|OUTRIGHT|NOT\s+CONFIRM|ALSO\s+RENT|WOT\s+LIFT)', re.IGNORECASE)
}

# Excel Formatting Settings
EXCEL_FORMATTING = {
    'header_color': 'FF4472C4',  # Blue
    'alternate_row_color': 'FFF2F2F2',  # Light gray
    'border_color': 'FF000000',  # Black
    'font_name': 'Calibri',
    'font_size': 11,
    'header_font_size': 12
}

# GUI Settings
GUI_CONFIG = {
    'window_title': 'PDF to Excel Converter - Real Estate Data',
    'window_size': '800x600',
    'theme_color': '#2E86AB',
    'button_color': '#A23B72',
    'success_color': '#F18F01',
    'error_color': '#C73E1D'
}

# File Processing Settings
PROCESSING_CONFIG = {
    'max_file_size_mb': 50,  # Maximum PDF file size in MB
    'batch_size': 10,  # Number of files to process at once
    'output_filename': 'Real_Estate_Data_Converted.xlsx'
} 